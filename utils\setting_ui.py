from PyQt5.QtCore import QRegExp
from PyQt5.QtGui import QColor, QTextCharFormat, QFont, QSyntaxHighlighter

qfont12 = QFont()
qfont12.setFamily('글림체')
qfont12.setPixelSize(12)

color_fg_bt = QColor(230, 230, 235)
color_fg_bc = QColor(190, 190, 195)
color_fg_dk = QColor(150, 150, 155)
color_fg_bk = QColor(110, 110, 115)
color_fg_hl = QColor(175, 175, 180)
color_fg_rt = QColor(110, 255, 110)

color_bg_bt = QColor(50, 50, 55)
color_bg_bc = QColor(40, 40, 45)
color_bg_dk = QColor(30, 30, 35)
color_bg_bk = QColor(20, 20, 25)

color_bf_bt = QColor(110, 110, 115)
color_bf_dk = QColor(70, 70, 75)
color_cs_hr = QColor(255, 255, 255)

style_fc_bt = 'color: rgb(230, 230, 235);'
style_fc_dk = 'color: rgb(150, 150, 155);'
style_bc_st = 'background-color: rgb(70, 70, 75);'
style_bc_bt = 'background-color: rgb(50, 50, 55);'
style_bc_dk = 'background-color: rgb(30, 30, 35);'
style_bc_by = 'background-color: rgb(100, 70, 70);'
style_bc_sl = 'background-color: rgb(70, 70, 100);'
style_bc_bs = 'background-color: rgb(70, 100, 70);'
style_bc_ba = 'background-color: rgb(70, 100, 100);'
style_pgbar = 'QProgressBar {background-color: #28282d;} QProgressBar::chunk {background-color: #5a5a5f;}'
style_pgbar2 = 'QProgressBar {background-color: #28282d;} QProgressBar::chunk {background-color: #3fcc6c;}'

def color_format(color, style=''):
    _format = QTextCharFormat()
    _format.setForeground(color)
    if 'bold' in style:
        _format.setFontWeight(QFont.Bold)
    if 'italic' in style:
        _format.setFontItalic(True)
    return _format

STYLES = {
    'keyword': color_format(QColor(255, 100, 0)),
    'operator': color_format(QColor(230, 230, 50)),
    'brace': color_format(QColor(230, 230, 240)),
    'defclass': color_format(QColor(230, 100, 0), 'bold'),
    'string': color_format(QColor(100, 230, 100)),
    'string2': color_format(QColor(100, 230, 100)),
    'string3': color_format(QColor(100, 230, 100)),
    'comment': color_format(QColor(100, 230, 100), 'italic'),
    'self': color_format(QColor(230, 50, 230)),
    'numbers': color_format(QColor(50, 230, 230)),
    'type': color_format(QColor(100, 100, 230))
}

class PythonHighlighter(QSyntaxHighlighter):
    keywords = [
        'and', 'assert', 'break', 'class', 'continue', 'def', 'del', 'elif', 'else', 'except', 'exec', 'finally',
        'for', 'from', 'global', 'if', 'import', 'in', 'is', 'lambda', 'not', 'or', 'pass',
        'raise', 'return', 'try', 'while', 'yield', 'None', 'True', 'False'
    ]
    types = ['int', 'float', 'round', 'str', 'boolean', 'datetime', 'print']
    operators = [
        '=', '==', '!=', '<', '<=', '>', '>=', '\+', '-', '\*', '/', '//', '\%', '\*\*',
        '\+=', '-=', '\*=', '/=', '\%=', '\^', '\|', '\&', '\~', '>>', '<<'
    ]
    braces = ['\{', '\}', '\(', '\)', '\[', '\]']
    string3 = ['"""']

    def __init__(self, document):
        QSyntaxHighlighter.__init__(self, document)
        self.tri_single = (QRegExp("'''"), 1, STYLES['string2'])
        self.tri_double = (QRegExp('"""'), 2, STYLES['string2'])

        rules = []
        rules += [(r'\b%s\b' % w, 0, STYLES['keyword']) for w in PythonHighlighter.keywords]
        rules += [(r'\b%s\b' % t, 0, STYLES['type']) for t in PythonHighlighter.types]
        rules += [(r'%s' % o, 0, STYLES['operator']) for o in PythonHighlighter.operators]
        rules += [(r'%s' % b, 0, STYLES['brace']) for b in PythonHighlighter.braces]
        rules += [(r'%s' % s, 0, STYLES['string3']) for s in PythonHighlighter.string3]
        rules += [
            (r'\bself\b', 0, STYLES['self']),
            (r'"[^"\\]*(\\.[^"\\]*)*"', 0, STYLES['string']),
            (r"'[^'\\]*(\\.[^'\\]*)*'", 0, STYLES['string']),
            (r'\bdef\b\s*(\w+)', 1, STYLES['defclass']),
            (r'\bclass\b\s*(\w+)', 1, STYLES['defclass']),
            (r'#[^\n]*', 0, STYLES['comment']),
            (r'\b[+-]?[0-9]+[lL]?\b', 0, STYLES['numbers']),
            (r'\b[+-]?0[xX][0-9A-Fa-f]+[lL]?\b', 0, STYLES['numbers']),
            (r'\b[+-]?[0-9]+(?:\.[0-9]+)?(?:[eE][+-]?[0-9]+)?\b', 0, STYLES['numbers'])
        ]
        self.rules = [(QRegExp(pat), index, fmt) for (pat, index, fmt) in rules]

    def highlightBlock(self, text):
        for expression, nth, format_ in self.rules:
            index = expression.indexIn(text)
            while index >= 0:
                index = expression.pos(nth)
                length = len(expression.cap(nth))
                self.setFormat(index, length, format_)
                index = expression.indexIn(text, index + length)
        self.setCurrentBlockState(0)

    def match_multiline(self, text, delimiter, in_state, style):
        if self.previousBlockState() == in_state:
            start = 0
            add = 0
        else:
            start = delimiter.indexIn(text)
            add = delimiter.matchedLength()

        while start >= 0:
            end = delimiter.indexIn(text, start + add)
            if end >= add:
                length = end - start + add + delimiter.matchedLength()
                self.setCurrentBlockState(0)
            else:
                self.setCurrentBlockState(in_state)
                length = len(text) - start + add
            self.setFormat(start, length, style)
            start = delimiter.indexIn(text, start + length)

        if self.currentBlockState() == in_state:
            return True
        else:
            return False
