from datetime import datetime
import datetime
from threading import Thread
from cryptography.fernet import Fernet
import winreg as reg
from PyQt5.QtTest import QTest


E_OPENAPI_PATH = 'C:/LS_SEC/xingAPI'

def thread_decorator(func):
    def wrapper(*args):
        Thread(target=func, args=args, daemon=True).start()
    return wrapper

def re_text(mill, text):
    fernet = Fernet(bytes(mill, 'utf-8'))
    return str(fernet.decrypt(bytes(text, 'utf-8')), 'utf-8')

def qtest_qwait(milsec):
    QTest.qWait(milsec)

def now():
    return datetime.datetime.now()

def timedelta_sec(second, std_time=None):
    return now() + datetime.timedelta(seconds=float(second)) if std_time is None else std_time + datetime.timedelta(seconds=float(second))

def timedelta_day(day, std_time=None):
    return now() + datetime.timedelta(days=float(day)) if std_time is None else std_time + datetime.timedelta(days=float(day))

def strp_time(timetype, str_time):
    return datetime.datetime.strptime(str_time, timetype)

def strf_time(timetype, std_time=None):
    return now().strftime(timetype) if std_time is None else std_time.strftime(timetype)


def int_hms():
    return int(strf_time('%H%M%S'))

def int_hms_utc():
    return int(strf_time('%H%M%S', timedelta_sec(-32400)))


def from_timestamp(time):
    return datetime.datetime.fromtimestamp(time)

def change_format(text, dotdowndel=False, dotdown4=False, dotdown8=False):
    text = str(text)
    try:
        format_data = f'{int(text):,}'
    except:
        if dotdowndel:
            format_data = f'{float(text):,.0f}'
        elif dotdown4:
            format_data = f'{float(text):,.4f}'
        elif dotdown8:
            format_data = f'{float(text):,.8f}'
        else:
            format_data = f'{float(text):,.2f}'
    return format_data

def comma2int(t):
    if '.' in t: t = t.split('.')[0]
    if ':' in t: t = t.replace(':', '')
    if ' ' in t: t = t.replace(' ', '')
    if ',' in t: t = t.replace(',', '')
    return int(t)

def comma2float(t):
    if ' ' in t: t = t.replace(' ', '')
    if ',' in t: t = t.replace(',', '')
    return float(t)

def read_me():
    openkey = reg.OpenKey(reg.HKEY_LOCAL_MACHINE, r'SOFTWARE\IOT\EN_KEY', 0, reg.KEY_ALL_ACCESS)
    key, _  = reg.QueryValueEx(openkey, 'EN_KEY')
    reg.CloseKey(openkey)
    return key

def GetOptionLongPgSgSp(bg, cg):
    # sfee = cg * 0.0005
    # bfee = bg * 0.0005
    sfee = 0
    bfee = 0
    pg   = int(round(cg - bfee - sfee))
    sg   = int(round(pg - bg))
    sp   = 0
    if bg != 0:
        sp   = round(sg / bg*100, 2)
    return pg, sg, sp

def GetOptionShortPgSgSp(bg, cg):
    # sfee = cg * 0.0005
    # bfee = bg * 0.0005
    sfee = 0
    bfee = 0
    pg   = int(round(cg - bfee - sfee))
    sg   = int(round(bg - pg))
    sp   = 0
    if bg != 0:
        sp   = round(sg / bg*100, 2)
    return pg, sg, sp




