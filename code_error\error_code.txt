code = str(code)
    ht = {
        "-1" : "통신소켓 생성에 실패하였습니다",
        "-2" : "서버접속에 실패하였습니다",
        "-3" : "서버주소가 틀렸습니다",
        "-4" : "서버 접속시간이 초과되었습니다",
        "-5" : "이미 서버에 연결중입니다",
        "-6" : "해당TR은 사용할수 없습니다",
        "-7" : "로그인을 해야 사용이 가능합니다",
        "-8" : "시세전용에서는 사용이 불가능합니다",
        "-9" : "해당 계좌번호를 가지고 있지 않습니다",
        "-10" : "패킷의 크기가 잘못되었습니다",
        "-11" : "Data의 크기가 다릅니다",
        "-12" : "계좌가 존재하지 않습니다",
        "-13" : "Request ID 부족",
        "-14" : "소켓이 생성되지 않았습니다",
        "-15" : "암호화 생성에 실패했습니다",
        "-16" : "데이터 전송에 실패했습니다",
        "-17" : "암호화(RTN)처리에 실패했습니다",
        "-18" : "공인인증 파일이 없습니다",
        "-19" : "공인인증 Function이 없습니다",
        "-20" : "메모리가 충분하지 않습니다",
        "-21" : "TR의 시간당 전송제한에 걸렸습니다",
        "-22" : "해당 TR은 해당 함수를 이용할 수 없습니다",
        "-23" : "로그인이 안되었거나, TR에 대한 정보를 찾을 수 없습니다",
        "-24" : "계좌위치가 지정되지 않았습니다",
        "-25" : "계좌를 가지고 있지 않습니다",
        "-26" : "파일 읽기에 실패했습니다 (종목 검색 조회 시, 파일이 없는 경우)",
        "0000" : "정상완료되었습니다",
        "00310" : "모의투자 조회가 완료되었습니다",
        "00136" : "조회가 완료되었습니다",
        "00020" : "application program exit[TR:CSPAQ]",
        "03669" : "비밀번호 오류입니다. (5회중 4회 남았습니다)",
        "01796" : "비밀번호 연속 오류허용횟수를 초과하였습니다. 콜센터로 문의하시기 바랍니다"
    }