from multiprocessing import Process, Queue
from option.OptionStrategy import OptionStrategy
import time

def test_strategy():
    """
    OptionStrategy 클래스를 테스트하는 간단한 스크립트
    """
    # 테스트용 큐 생성
    windowQ = Queue()
    receiverQ = Queue()
    traderQ = Queue()
    CallStrQ = Queue()
    PutStrQ = Queue()
    teleQ = Queue()
    
    # 큐 리스트 생성
    qlist = [windowQ, receiverQ, traderQ, CallStrQ, PutStrQ, teleQ]
    
    # 콜 옵션 전략 프로세스 시작
    call_process = Process(target=OptionStrategy, args=(qlist, "call"), daemon=True)
    call_process.start()
    print("콜 옵션 전략 프로세스 시작")
    
    # 풋 옵션 전략 프로세스 시작
    put_process = Process(target=OptionStrategy, args=(qlist, "put"), daemon=True)
    put_process.start()
    print("풋 옵션 전략 프로세스 시작")
    
    # 메시지 모니터링
    try:
        while True:
            if not windowQ.empty():
                msg = windowQ.get()
                print(f"윈도우 메시지: {msg}")
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("테스트 종료")
    finally:
        # 프로세스 종료
        CallStrQ.put("전략프로세스종료")
        PutStrQ.put("전략프로세스종료")
        call_process.join(timeout=3)
        put_process.join(timeout=3)
        
        if call_process.is_alive():
            call_process.terminate()
        if put_process.is_alive():
            put_process.terminate()
        
        print("모든 프로세스 종료")

if __name__ == "__main__":
    test_strategy() 