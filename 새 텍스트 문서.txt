pyinstaller -w --uac-admin --onefile --noconsole -F -n OptionTraderM.exe -c --clean OptionTraderM.py

a = Analysis(
    ['OptionTraderM.py'],
    pathex=[],
    binaries=[],
    datas=[('./icon/*', './icon'),
            ('./utils/*', './'),
            ('config_setting.ini', './'),
	('창위치.txt', './'),
    ],		
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyinstaller -w --uac-admin --onefile --debug all --noconsole -F -n OptionTraderM.exe -c --clean OptionTraderM.py
--add-binary="C:\LS_SEC\xingAPI\XA_Session.dll;."

a = Analysis(
    ['test.py'],
    pathex=[],
    binaries=['C:/LS_SEC/xingAPI/*', './'],
    datas=[],
    hiddenimports=['C:/LS_SEC/xingAPI/SKComdCM.dll', 'C:/LS_SEC/xingAPI/SKComdEM.dll','C:/LS_SEC/xingAPI/SKComdIF.dll','C:/LS_SEC/xingAPI/SKComdSC.dll','C:/LS_SEC/xingAPI/SKCommCM.dll','C:/LS_SEC/xingAPI/SKCommEM.dll','C:/LS_SEC/xingAPI/SKCommIC.dll','C:/LS_SEC/xingAPI/SKCommIF.dll','C:/LS_SEC/xingAPI/SKCommSC.dll'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)