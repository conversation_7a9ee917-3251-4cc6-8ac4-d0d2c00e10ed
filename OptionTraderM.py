import sys
import os
from utils.util import *
from utils.config import *
from option.xingAPI import *
from option.receiver import Receiver
from option.trader import Trader
from option.optionstrategy import OptionStrategy
import subprocess
from multiprocessing import Process, Queue, freeze_support
from PyQt5.QtCore import QThread, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtGui import QPalette
from PyQt5.QtWidgets import QMainWindow, QMessageBox, QTableWidgetItem, QApplication, QDialog
from multiprocessing import Process, Queue
from utils.setui import *
from utils.setting_ui import *
from utils.configparserINI import ConfigparserINI
from utils.telegram_msg import TelegramMsg
import time


class Writer(QThread):
    signal1 = pyqtSignal(list)
    signal2 = pyqtSignal(list)

    def __init__(self):
        super().__init__()

    def run(self):
        while True:
            data = windowQ.get()
            if data[0] in [ui_num['단순텍스트'], ui_num['로그텍스트']]: 
                self.signal1.emit(data)
            elif data[0] in [ui_num['실현손익'], ui_num['잔고목록'], ui_num['체결목록']]:
                self.signal2.emit(data)

class Window(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.dict_set = DICT_SET
        self.INI = ConfigparserINI()
        self.xas = XASession()

        self.receiverTime = int(self.INI.receiverTime)
        self.traderTime   = int(self.INI.traderTime)
        self.exitTime     = int(self.INI.exitTime)
        self.THU          = int(self.INI.THU)
        self.MON          = int(self.INI.MON)
        self.MoOption     = int(self.INI.MoOption)
        
        self.int_time = int(strf_time('%H%M%S'))
        self.str_tday   = strf_time('%Y%m%d') 
        
        setui = SetUI(self)
        setui.SetupUI()
 
        self.proc_receiver      = None
        self.proc_trader        = None
        self.proc_strategyCall  = None
        self.proc_strategyPut   = None
           
        self.qtimer = QTimer()
        self.qtimer.setInterval(1 * 1000)
        self.qtimer.timeout.connect(self.ProcessStarter)
        self.qtimer.start()

        # subprocess.Popen('python ./utils/timesync.py')
        
        self.writer = Writer()
        self.writer.signal1.connect(self.UpdateTexedit)
        self.writer.signal2.connect(self.UpdateTablewidget)
        self.writer.start()
       
    def ProcessStarter(self):
        if self.int_time < 83600 <= int(strf_time('%H%M%S')): # 테스트 필요
            self.ClearTextEdit()   

        if self.int_time < self.receiverTime <= int(strf_time('%H%M%S'))  and not self.dict_set['리시버']:
            self.OptionReceiverStart() 
        if self.int_time < self.traderTime <= int(strf_time('%H%M%S')) and not self.dict_set['트레이더']: 
            self.OptionTraderStart()

        self.UpdateWindowTitle()               
        self.int_time = int(strf_time('%H%M%S')) # 테스트 필요
        
        if self.exitTime <= int(strf_time('%H%M%S')): 
            self.shutdown()
                
    def OptionReceiverStart(self):
        start = False
        if self.proc_receiver is None or not self.proc_receiver.is_alive():

            self.proc_receiver = Process(target=Receiver, args=(qlist,), daemon=True)
            self.proc_receiver.start()
            start = True
        if start:
            self.dict_set['리시버'] = True
            
            windowQ.put([ui_num['단순텍스트'], '리시버를 시작하였습니다.'])
        else:
            QMessageBox.critical(
                self, '오류 알림', '두번째 계정이 설정되지 않아\n리시버를 시작할 수 없습니다.\n계정 설정 후 다시 시작하십시오.\n')
    
    def OptionTraderStart(self):
        start = False
        if self.proc_strategyCall is None or not self.proc_strategyCall.is_alive():
            self.proc_strategyCall = Process(target=OptionStrategy, args=(qlist, "call"), daemon=True)
            self.proc_strategyCall.start()
            windowQ.put([ui_num['단순텍스트'], '콜 전략를 시작하였습니다.'])
        if self.proc_strategyPut is None or not self.proc_strategyPut.is_alive():
            self.proc_strategyPut = Process(target=OptionStrategy, args=(qlist, "put"), daemon=True)
            self.proc_strategyPut.start()
            windowQ.put([ui_num['단순텍스트'], '풋 전략를 시작하였습니다.'])
        if self.proc_trader is None or not self.proc_trader.is_alive():
            self.proc_trader = Process(target=Trader, args=(qlist,), daemon=True)
            self.proc_trader.start()
            start = True
        if start:
            self.dict_set['트레이더'] = True
            windowQ.put([ui_num['단순텍스트'], '트레이더를 시작하였습니다.'])
        else:
            QMessageBox.critical(
                self, '오류 알림', '첫번째 계정이 설정되지 않아\n트레이더를 시작할 수 없습니다.\n계정 설정 후 다시 시작하십시오.\n')
    
    def UpdateWindowTitle(self):
        if self.THU:
            self.wintitle = 'LS증권 WeeklyOption (목요일 만기)'  
        elif self.MON:
            self.wintitle = 'LS증권 WeeklyOption (월요일 만기)'  
        elif self.MoOption:
            self.wintitle = 'LS증권 MonatlyOption (월옵션)'
            
        text = self.wintitle
        text = f"{text} | {strf_time('%Y-%m-%d %H:%M:%S')}"
        self.setWindowTitle(text)

    def ClearTextEdit(self):
        self.st_textEdit.clear()
        self.svc_textEdit.clear()
        
    def UpdateTexedit(self, data):         
        if len(data) == 2:
            timeHMS = strf_time('%H:%M:%S')
            text = f'[{timeHMS}] {data[1]}'
            if '시스템 명령 오류' in data[1]:
                self.lgicon_alert = True

            if data[0] == ui_num['로그텍스트']:
                self.st_textEdit.append(text)
            elif data[0] == ui_num['단순텍스트']:
                self.svc_textEdit.append(text)
              
            if data[0] == ui_num['로그텍스트'] and '리시버 종료' in data[1]:
                if self.proc_receiver is not None and self.proc_receiver.is_alive():
                    self.proc_receiver.kill()
            elif data[0] == ui_num['로그텍스트'] and '트레이더 종료' in data[1]:
                if self.proc_trader is not None and self.proc_trader.is_alive():
                    self.proc_trader.kill()
            if data[0] == ui_num['로그텍스트'] and '콜전략 종료' in data[1]:
                if self.proc_strategyCall is not None and self.proc_strategyCall.is_alive():
                    self.proc_strategyCall.kill()
            elif data[0] == ui_num['로그텍스트'] and '풋전략 종료' in data[1]:
                if self.proc_strategyPut is not None and self.proc_strategyPut.is_alive():
                    self.proc_strategyPut.kill()
                    
    def UpdateTablewidget(self, data):
        gubun, df = data
        tableWidget = None

        if gubun == ui_num['실현손익']:
            tableWidget = self.scj_tableWidget
        elif gubun == ui_num['잔고목록']:
            tableWidget = self.sjg_tableWidget
        elif gubun == ui_num['잔고평가']:
            tableWidget = self.stj_tableWidget       
        if tableWidget is None:
            return
        if len(df) == 0:
            tableWidget.clearContents()
            return
        
        tableWidget.setRowCount(len(df))
        for j, index in enumerate(df.index):
            for i, column in enumerate(df.columns):
                if column in ['당일실현손익','평가손익', '매입금액', '옵션평가금액', '보유수량', '평가금액']:
                    item = QTableWidgetItem(change_format(df[column][index], dotdowndel=True))  
                    item.setTextAlignment((Qt.AlignVCenter | Qt.AlignCenter))
                elif column in ['수익율', '현재가', '매입가'] :   
                    item = QTableWidgetItem(change_format(df[column][index]))
                    item.setTextAlignment((Qt.AlignVCenter | Qt.AlignCenter))     
                elif column in ['종목코드', '구분']:
                    try:
                        item = QTableWidgetItem(str(df[column][index]))  
                        item.setTextAlignment((Qt.AlignVCenter | Qt.AlignCenter))
                    except:
                        pass
                else:
                    item = QTableWidgetItem(str(df[column][index]))  
                    item.setTextAlignment((Qt.AlignVCenter | Qt.AlignCenter))   
                tableWidget.setItem(j, i, item)

        if len(df) < 13 and gubun in [ui_num['잔고목록']]:
            tableWidget.setRowCount(13)
        elif len(df) < 15 and gubun in [ui_num['실현손익']]:
            tableWidget.setRowCount(15)       
                          
    @pyqtSlot(int)
    def TabChanged(self, i):
        if i == 4 and self.lgicon_alert:
            self.lgicon_alert = False
            self.main_tabWidget.setTabIcon(4, self.icon_log)
    
    
    @pyqtSlot(int)
    def CellClicked_02(self, row):
        item = self.sjg_tableWidget.item(row, 0)
        if item is None:
            return
        name = item.text()
        optcode = self.sjg_tableWidget.item(row, columns_jg.index('종목코드')).text()
        oc = comma2int(self.sjg_tableWidget.item(row, columns_jg.index('보유수량')).text())
        c = comma2float(self.sjg_tableWidget.item(row, columns_jg.index('현재가')).text())
        dir = self.sjg_tableWidget.item(row, columns_jg.index('구분')).text()
        if dir == "매수":
            on = "1"
            dirname = "매도"
            cp = c - 0.1
        elif dir == "매도":
            on = "2"
            dirname = "매수"
            cp = c + 0.1
        buttonReply = QMessageBox.question(
            self, f'시장가 {dirname}', f'{name} {oc}계약을 시장가{dirname}합니다.\n계속하시겠습니까?\n',
            
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if buttonReply == QMessageBox.Yes:
            if self.proc_trader is not None and self.proc_trader.is_alive():
                traderQ.put(['수동청산', optcode, on, cp, oc, now()])    
    
    ########################### 전략 시작 작업 ##############################
    def sjButtonClicked_07(self):
        buttonReply = QMessageBox.question(
            self, '전략시작',
            '시스템을 시작합니다. 계속하시겠습니까?\n',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if buttonReply == QMessageBox.Yes:
            self.ClearTextEdit()  
            if 5 <= int(strf_time('%H%M%S')) and self.proc_receiver is None:
                self.OptionReceiverStart()
            time.sleep(3)
            if 5 <= int(strf_time('%H%M%S')) and self.proc_trader is None: 
                self.OptionTraderStart()

            self.sj_stopbegin_pushButton_07.setStyleSheet(style_bc_dk)
                
    ########################### 전략 시작 END ##############################
    
    def sjButtonClicked_17(self):
        if 84500 <= int(strf_time('%H%M%S')):
            buttonReply = QMessageBox.question(
                self, '일괄청산',
                '일괄청산을 합니다. 계속하시겠습니까?\n',
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            if buttonReply == QMessageBox.Yes:
                traderQ.put(['잔고청산'])   
                
    def CheckboxChanged_01(self, state):
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'demo_market', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'demo_market', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile) 
                
    def CheckboxChanged_02(self, state):
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'real_market', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'real_market', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)   

    def CheckboxChanged_03(self, state):
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'telegram', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'telegram', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)    
    
    def CheckboxChanged_04(self, state): 
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'realminstrategy', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'realminstrategy', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
    
    def CheckboxChanged_05(self, state):
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'minstrategy', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'minstrategy', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
                
    def CheckboxChanged_06(self, state): 
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'mon', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'mon', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
    
    def CheckboxChanged_07(self, state): 
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'thu', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'thu', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
                
    def CheckboxChanged_08(self, state): 
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'mooption', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('ServerSelection', 'mooption', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)  
                
    def CheckboxChanged_09(self, state): 
        config = configparser.ConfigParser()
        if state == Qt.Checked:
            config.read('setting_config.ini')
            config.set('StragVar2', 'sellconduse', '1')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
        else:
            config.read('setting_config.ini')
            config.set('StragVar2', 'sellconduse', '0')
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)   

    def sjButtonClicked_03(self):
        INI = ConfigparserINI()
        self.sj_options_lineEdit_01.setText(INI.strategy_begintime)
        self.sj_options_lineEdit_02.setText(INI.strategy_endtime)  
        self.sj_options_lineEdit_03.setText(INI.selltime)
        self.sj_options_lineEdit_06.setText(INI.receiverTime)
        self.sj_options_lineEdit_07.setText(INI.traderTime) 
        self.sj_options_lineEdit_08.setText(INI.exitTime)

    def sjButtonClicked_04(self):
        INI = ConfigparserINI()  
        self.sj_allvar_lineEdit_01.setText(INI.priceMin)
        self.sj_allvar_lineEdit_02.setText(INI.priceMax) 
        self.sj_allvar_lineEdit_03.setText(INI.jangChoInvest)
        self.sj_allvar_lineEdit_04.setText(INI.Bong)
        self.sj_allvar_lineEdit_05.setText(INI.ResetTr)

    def sjButtonClicked_06(self):
        INI = ConfigparserINI()
        self.sj_allvar2_lineEdit_01.setText(INI.stoch_period1)
        self.sj_allvar2_lineEdit_02.setText(INI.stoch_period2)
        self.sj_allvar2_lineEdit_03.setText(INI.stoch_period3)  
        self.sj_allvar2_lineEdit_04.setText(INI.stoch_level)  
        self.sj_allvar2_lineEdit_08.setText(INI.cci_period)   
        self.sj_allvar2_lineEdit_09.setText(INI.cci_level)
        self.sj_allvar2_lineEdit_10.setText(INI.scci_period)   
        self.sj_allvar2_lineEdit_11.setText(INI.scci_level)
        self.sj_allvar2_lineEdit_15.setText(INI.dmi_period) 
        self.sj_allvar2_lineEdit_16.setText(INI.adx_period)
        self.sj_allvar2_lineEdit_17.setText(INI.adx_level)
        self.sj_allvar2_lineEdit_18.setText(INI.sell_adx_level)
        
    def sjButtonClicked_05(self):
        INI = ConfigparserINI()
        self.sj_ptsl_lineEdit_01.setText(INI.stoploss)
        self.sj_ptsl_lineEdit_02.setText(INI.absProfitTarget)  
    
    def sjButtonClicked_11(self):
        sbegin = self.sj_options_lineEdit_01.text()
        send = self.sj_options_lineEdit_02.text()
        sell = self.sj_options_lineEdit_03.text()
        receivertime = self.sj_options_lineEdit_06.text()
        tradertime = self.sj_options_lineEdit_07.text()
        exittime = self.sj_options_lineEdit_08.text()

        if '' in [sbegin, send, sell, receivertime, tradertime, exittime]:
            QMessageBox.critical(self, '오류 알림', '일부 설정값이 입력되지 않았습니다.\n')
        else:
            config = configparser.ConfigParser()
            config.read('setting_config.ini')
            config.set('StragTime', 'strategy_begintime', sbegin)
            config.set('StragTime', 'strategy_endtime', send)
            config.set('StragTime', 'selltime', sell)
            config.set('StragTime', 'receivertime', receivertime)
            config.set('StragTime', 'tradertime', tradertime)
            config.set('StragTime', 'exittime', exittime)
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)

    def sjButtonClicked_12(self):        
        priceMin = self.sj_allvar_lineEdit_01.text()
        priceMax = self.sj_allvar_lineEdit_02.text()   
        jangchoInvest = self.sj_allvar_lineEdit_03.text()     
        bong = self.sj_allvar_lineEdit_04.text()
        resettr = self.sj_allvar_lineEdit_05.text()

        if '' in [jangchoInvest, priceMin, priceMax, bong, resettr]:
            QMessageBox.critical(self, '오류 알림', '일부 설정값이 입력되지 않았습니다.\n')
        else:
            config = configparser.ConfigParser()
            config.read('setting_config.ini')
            config.set('StragVar', 'jangchoinvest', jangchoInvest)
            config.set('StragVar', 'pricemin', priceMin)
            config.set('StragVar', 'pricemax', priceMax)
            config.set('StragVar', 'bong', bong) 
            config.set('StragVar', 'resettr', resettr)               
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)

    def sjButtonClicked_14(self):
        stoch_period1 = self.sj_allvar2_lineEdit_01.text()
        stoch_period2 = self.sj_allvar2_lineEdit_02.text()
        stoch_period3 = self.sj_allvar2_lineEdit_03.text()
        stoch_level = self.sj_allvar2_lineEdit_04.text()
        cci_period = self.sj_allvar2_lineEdit_08.text()
        cci_level = self.sj_allvar2_lineEdit_09.text()
        scci_period = self.sj_allvar2_lineEdit_10.text()
        scci_level = self.sj_allvar2_lineEdit_11.text()
        dmi_period = self.sj_allvar2_lineEdit_15.text()
        adx_period = self.sj_allvar2_lineEdit_16.text()
        adx_level = self.sj_allvar2_lineEdit_17.text()
        sell_adx_level = self.sj_allvar2_lineEdit_18.text()

        if '' in [stoch_period1, stoch_period2, stoch_period3, stoch_level, cci_period, cci_level, scci_period, scci_level, dmi_period, adx_period, adx_level, sell_adx_level]:
            QMessageBox.critical(self, '오류 알림', '일부 설정값이 입력되지 않았습니다.\n')
        else:
            config = configparser.ConfigParser()
            config.read('setting_config.ini')
            config.set('StragVar2', 'stoch_period1', stoch_period1)
            config.set('StragVar2', 'stoch_period2', stoch_period2)
            config.set('StragVar2', 'stoch_period3', stoch_period3)
            config.set('StragVar2', 'stoch_level', stoch_level)
            config.set('StragVar2', 'cci_period', cci_period)
            config.set('StragVar2', 'cci_level', cci_level)
            config.set('StragVar2', 'scci_period', scci_period)
            config.set('StragVar2', 'scci_level', scci_level)
            config.set('StragVar2', 'dmi_period', dmi_period)
            config.set('StragVar2', 'adx_period', adx_period)   
            config.set('StragVar2', 'adx_level', adx_level) 
            config.set('StragVar2', 'sell_adx_level', sell_adx_level)   
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)

    def sjButtonClicked_13(self):
        stopLoss = self.sj_ptsl_lineEdit_01.text()
        absProfitTarget = self.sj_ptsl_lineEdit_02.text()

        if '' in [stopLoss,absProfitTarget]:
            QMessageBox.critical(self, '오류 알림', '일부 설정값이 입력되지 않았습니다.\n')
        else:
            config = configparser.ConfigParser()
            config.read('setting_config.ini')
            config.set('StragPTSL', 'stoploss', stopLoss)
            config.set('StragPTSL', 'absprofittarget', absProfitTarget)
            with open('setting_config.ini', 'w') as configfile:
                config.write(configfile)
            self.UpdateTexedit([ui_num['설정로그'], '전략변수 설정값 저장하기 완료']) 
                
    ############################### 계정 보안 #############################
    def sjButtonClicked_08(self):
        if self.sj_etc_pushButtonn_01.text() == '계정 텍스트 보기':
            self.sj_sacc_lineEdit_01.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_02.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_03.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_04.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_05.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_06.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_07.setEchoMode(QLineEdit.Normal)
            self.sj_sacc_lineEdit_08.setEchoMode(QLineEdit.Normal)
            self.sj_etc_pushButtonn_01.setText('계정 텍스트 가리기')
            self.sj_etc_pushButtonn_01.setStyleSheet(style_bc_dk)
        else:
            self.sj_sacc_lineEdit_01.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_02.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_03.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_04.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_05.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_06.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_07.setEchoMode(QLineEdit.Password)
            self.sj_sacc_lineEdit_08.setEchoMode(QLineEdit.Password)
            self.sj_etc_pushButtonn_01.setText('계정 텍스트 보기')
            self.sj_etc_pushButtonn_01.setStyleSheet(style_bc_bt)
        ############################# 계정 보안 끝 #############################
    
    def LoadSettings(self):
        self.sjButtonClicked_03()
        self.sjButtonClicked_04()
        self.sjButtonClicked_05()
        self.sjButtonClicked_06()

    def sjButtonClicked_30(self):
        self.LoadSettings()
        self.sj_log_labelll_00.setText('설정파일 로딩 완료')

    def sjButtonClicked_31(self):
        INI = ConfigparserINI()
        pw = self.sj_stopbegin_pushButton_19.text()
        if INI.TradePW != pw:
            QMessageBox.critical(self, '오류 알림', '리시버용 계좌 암호을 입력하세요!!!.\n')
        else:
            self.sjButtonClicked_11()
            self.sjButtonClicked_12()
            self.sjButtonClicked_13() 
            self.sjButtonClicked_14()    
            self.sj_log_labelll_00.setText('설정파일 저장 완료')    
        
    def mnButtonClicked_03(self):
        if self.geometry().height() > 400:
            self.setFixedSize(860, 172)
            self.zo_pushButton.setStyleSheet(style_bc_dk)
        else:
            self.setFixedSize(860, 510)
            self.zo_pushButton.setStyleSheet(style_bc_bt)
    
    def closeEvent(self, a):
        buttonReply = QMessageBox.question(
            self, "프로그램 종료", "프로그램을 종료합니다.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        if buttonReply == QMessageBox.Yes:
            self.ProcessKill()
            a.accept()
        else:
            a.ignore()
            
    def ProcessKill(self, sysexit=False):
        geometry = f'{self.x()};{self.y()};'       
        filePath = './창위치.txt'
        with open(filePath, 'w', encoding="UTF-8") as f:
            f.write(f"{geometry}")
            
        if self.writer.isRunning():
            self.writer.terminate()
        if self.qtimer.isActive():
            self.qtimer.stop()
        if sysexit:
            sys.exit()
        self.xas.logout()
        self.xas.disconnect_server()
    
    def shutdown(self):
        self.ProcessKill()
        qtest_qwait(20*1000)
        QApplication.quit()

if __name__ == '__main__':
    import ctypes
    freeze_support()
    kernel32 = ctypes.windll.kernel32
    kernel32.SetConsoleMode(kernel32.GetStdHandle(-10), 128)
    os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = "--blink-settings=forceDarkModeEnabled=true"
    subprocess.Popen('python ./utils/timesync.py')

    # 환경 변수 'PATH'의 현재 값을 다시 가져옵니다. (대소문자 수정)
    path_env = os.environ.get('Path')  # Windows에서는 대소문자를 구분하지 않지만, 'PATH'로 통일합니다.

    # 추가하고자 하는 경로
    new_path = r'C:\LS_SEC\xingAPI'
    
    # 'PATH' 환경 변수에 해당 경로가 이미 있는지 다시 확인합니다.
    if new_path not in path_env:
        # 경로가 없으면 추가합니다.
        updated_path = path_env + ";" + new_path
        # 환경 변수 'PATH'를 업데이트합니다. (주의: 이 변경은 현재 프로세스에만 영향을 줍니다.)
        os.environ['PATH'] = updated_path
        result = "경로가 추가되었습니다."
    else:
        result = "경로가 이미 환경 변수에 존재합니다."

    print("result: ", result)

    windowQ, receiverQ, traderQ, CallStrQ, PutStrQ, teleQ = Queue(), Queue(), Queue(), Queue(), Queue(), Queue()
    qlist = [windowQ, receiverQ, traderQ, CallStrQ, PutStrQ, teleQ]

    INI = ConfigparserINI() 
    if INI.telegram == "1":
        proc_tele  = Process(target=TelegramMsg, args=(qlist,), daemon=True)
        proc_tele.start()
    
    app = QApplication(sys.argv)
    app.setStyle(ProxyStyle())
    app.setStyle('fusion')
    palette = QPalette()
    palette.setColor(QPalette.Window, color_bg_bc)
    palette.setColor(QPalette.Background, color_bg_bc)
    palette.setColor(QPalette.WindowText, color_fg_bc)
    palette.setColor(QPalette.Base, color_bg_bc)
    palette.setColor(QPalette.AlternateBase, color_bg_dk)
    palette.setColor(QPalette.Text, color_fg_bc)
    palette.setColor(QPalette.Button, color_bg_bc)
    palette.setColor(QPalette.ButtonText, color_fg_bc)
    palette.setColor(QPalette.Link, color_fg_bk)
    palette.setColor(QPalette.Highlight, color_fg_hl)
    palette.setColor(QPalette.HighlightedText, color_bg_bk)
    app.setPalette(palette)
    window = Window()
    window.show()
    app.exec_()  
    