import pyqtgraph
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt, QRect, QPoint
from PyQt5.QtWidgets import QTabBar, QStylePainter, QStyle, QTabWidget, QProxyStyle, QStyleOptionTab, QPushButton, \
    QDialog, QTextEdit, QComboBox, QCheckBox, QLineEdit, QTableWidget, QAbstractItemView, \
    QWidget, QLabel, QGroupBox

from utils.util import *
from utils.config import *
from utils.setting_ui import qfont12, style_bc_bt, style_bc_st, style_bc_by, style_bc_sl, \
    style_bc_bs, style_bc_ba, style_bc_dk, style_fc_bt, PythonHighlighter
from utils.configparserINI import *


class TabBar(QTabBar):
    def tabSizeHint(self, index):
        s = QTabBar.tabSizeHint(self, index)
        s.setWidth(40)
        s.setHeight(40)
        s.transpose()
        return s

    def paintEvent(self, event):
        painter = QStylePainter(self)
        opt = QStyleOptionTab()

        for i in range(self.count()):
            self.initStyleOption(opt, i)
            painter.drawControl(QStyle.CE_TabBarTabShape, opt)
            painter.save()

            s = opt.rect.size()
            s.transpose()
            r = QRect(QPoint(), s)
            r.moveCenter(opt.rect.center())
            opt.rect = r

            c = self.tabRect(i).center()
            painter.translate(c)
            painter.rotate(90)
            painter.translate(-c)
            painter.drawControl(QStyle.CE_TabBarTabLabel, opt)
            painter.restore()


class TabWidget(QTabWidget):
    def __init__(self, *args, **kwargs):
        QTabWidget.__init__(self, *args, **kwargs)
        self.setTabBar(TabBar(self))
        self.setTabPosition(QTabWidget.West)


class ProxyStyle(QProxyStyle):
    def drawControl(self, element, opt, painter, widget=None):
        if element == QStyle.CE_TabBarTabLabel:
            ic = self.pixelMetric(QStyle.PM_TabBarIconSize)
            r = QRect(opt.rect)
            w = 0 if opt.icon.isNull() else opt.rect.width() + ic
            r.setHeight(opt.fontMetrics.width(opt.text) + w)
            r.moveBottom(opt.rect.bottom())
            opt.rect = r
        QProxyStyle.drawControl(self, element, opt, painter, widget)


class CustomViewBox(pyqtgraph.ViewBox):
    def __init__(self, *args, **kwds):
        pyqtgraph.ViewBox.__init__(self, *args, **kwds)
        self.setMouseMode(self.RectMode)
        self.setMouseEnabled(x=False, y=False)

    def mouseClickEvent(self, ev):
        if ev.button() == Qt.RightButton:
            self.enableAutoRange()


class SetUI:
    def __init__(self, ui_class):
        self.ui = ui_class

        self.dict_set = DICT_SET
        self.INI = ConfigparserINI()
        self.real_market = self.INI.real_market
        self.demo_market = self.INI.demo_market
        self.telegram = self.INI.telegram
        self.realMinStrategy = self.INI.realMinStrategy
        self.minStrategy = self.INI.minStrategy
        self.THU = self.INI.THU
        self.MON = self.INI.MON
        self.MoOption = self.INI.MoOption

        self.strategy_begintime = self.INI.strategy_begintime
        self.strategy_endtime = self.INI.strategy_endtime
        self.selltime = self.INI.selltime      
        self.receiverTime = self.INI.receiverTime 
        self.traderTime = self.INI.traderTime
        self.exitTime = self.INI.exitTime

        self.JangChoInvest = self.INI.jangChoInvest
        self.priceMin = self.INI.priceMin
        self.priceMax = self.INI.priceMax   
        self.Bong = self.INI.Bong     

        self.stoch_period1 = self.INI.stoch_period1
        self.stoch_period2 = self.INI.stoch_period2
        self.stoch_period3 = self.INI.stoch_period3
        self.stoch_level = self.INI.stoch_level
        self.cci_period = self.INI.cci_period
        self.cci_level = self.INI.cci_level
        self.scci_period = self.INI.scci_period
        self.scci_level = self.INI.scci_level
        self.dmi_period = self.INI.dmi_period
        self.adx_period = self.INI.adx_period
        self.adx_level = self.INI.adx_level
        self.sellCondUse = self.INI.sellCondUse
        self.ResetTr = self.INI.ResetTr
        self.sell_adx_level = self.INI.sell_adx_level

        self.stoploss = self.INI.stoploss
        self.absProfitTarget = self.INI.absProfitTarget
        
        if self.dict_set['창위치'] is not None:
            try:
                self.ui.move(self.dict_set['창위치'][0], self.dict_set['창위치'][1])
            except:
                pass

    def SetupUI(self):
        
        def setPushbutton(pname, box=None, click=None, cmd=None, icon=None, tip=None, shortcut=None, color=0,
                          visible=True):
            if box is not None:
                pushbutton = QPushButton(pname, box)
            else:
                pushbutton = QPushButton(pname, self.ui)
            if color == 0:
                pushbutton.setStyleSheet(style_bc_bt)
            elif color == 1:
                pushbutton.setStyleSheet(style_bc_st)
            elif color == 2:
                pushbutton.setStyleSheet(style_bc_by)
            elif color == 3:
                pushbutton.setStyleSheet(style_bc_sl)
            elif color == 4:
                pushbutton.setStyleSheet(style_bc_bs)
            elif color == 5:
                pushbutton.setStyleSheet(style_bc_ba)
            elif color == 6:
                pushbutton.setStyleSheet(style_bc_dk)
            pushbutton.setFont(qfont12)
            if click is not None:
                if cmd is not None:
                    pushbutton.clicked.connect(lambda: click(cmd))
                else:
                    pushbutton.clicked.connect(click)
            if icon is not None:
                pushbutton.setIcon(icon)
            if tip is not None:
                pushbutton.setToolTip(tip)
            if shortcut is not None:
                pushbutton.setShortcut(shortcut)
            if not visible:
                pushbutton.setVisible(False)
            return pushbutton

        def setTextEdit(tab, visible=True, font=None, vscroll=False, filter_=False):
            textedit = QTextEdit(tab)
            if filter_:
                textedit.installEventFilter(self.ui)
                PythonHighlighter(textedit)
            else:
                textedit.setReadOnly(True)
            if not vscroll:
                textedit.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            textedit.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            if font is not None:
                textedit.setFont(font)
            if not visible:
                textedit.setVisible(visible)
            return textedit

        def setCombobox(tab, font=None, activated=None, items=None, tip=None, visible=True):
            combobox = QComboBox(tab)
            if font is not None:
                combobox.setFont(font)
            if activated is not None:
                combobox.currentTextChanged.connect(activated)
            if items is not None:
                for item in items:
                    combobox.addItem(item)
            if tip is not None:
                combobox.setToolTip(tip)
            if not visible:
                combobox.setVisible(visible)
            return combobox

        def setCheckBox(cname, groupbox, changed=None, checked=False, tip=None):
            checkbox = QCheckBox(cname, groupbox)
            if changed is not None:
                checkbox.stateChanged.connect(changed)
            if checked:
                checkbox.setChecked(checked)
            if tip is not None:
                checkbox.setToolTip(tip)
            return checkbox

        def setLineedit(groupbox, enter=None, passhide=False, ltext=None, style=None, tip=None, font=None, aleft=False):
            lineedit = QLineEdit(groupbox)
            lineedit.setStyleSheet(style_fc_bt)
            if aleft:
                lineedit.setAlignment(Qt.AlignLeft)
            else:
                lineedit.setAlignment(Qt.AlignRight)
            if font is not None:
                lineedit.setFont(font)
            else:
                lineedit.setFont(qfont12)
            if enter:
                lineedit.returnPressed.connect(enter)
            if passhide:
                lineedit.setEchoMode(QLineEdit.Password)
            if ltext is not None:
                lineedit.setText(ltext)
            if style is not None:
                lineedit.setStyleSheet(style)
            if tip is not None:
                lineedit.setToolTip(tip)
            return lineedit

        def setTablewidget(tab, columns, rowcount, clicked=None, vscroll=False, visible=True, sorting=False):
            tableWidget = QTableWidget(tab)
            tableWidget.verticalHeader().setDefaultSectionSize(23)
            tableWidget.verticalHeader().setVisible(False)
            tableWidget.setAlternatingRowColors(True)
            tableWidget.setSelectionMode(QAbstractItemView.NoSelection)
            tableWidget.setEditTriggers(QAbstractItemView.NoEditTriggers)
            if not vscroll:
                tableWidget.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            tableWidget.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
            tableWidget.setColumnCount(len(columns))
            tableWidget.setRowCount(rowcount)
            tableWidget.setHorizontalHeaderLabels(columns)
            if clicked is not None:
                tableWidget.cellClicked.connect(clicked)
            if not visible:
                tableWidget.setVisible(False)
            if sorting:
                tableWidget.setSortingEnabled(True)
                
            if columns[-1] == '구분':
                if tab == self.ui.lg_tab:
                    tableWidget.setColumnWidth(0, 122)
                    tableWidget.setColumnWidth(1, 84)
                    tableWidget.setColumnWidth(2, 84)
                    tableWidget.setColumnWidth(3, 84)
                    tableWidget.setColumnWidth(4, 84)
                    tableWidget.setColumnWidth(5, 84)
                    tableWidget.setColumnWidth(6, 84)
                    tableWidget.setColumnWidth(7, 84)
                    tableWidget.setColumnWidth(8, 90)
                    tableWidget.setColumnWidth(9, 84)
                    tableWidget.setColumnWidth(10, 84)
                    tableWidget.setColumnWidth(11, 84)
                    tableWidget.setColumnWidth(12, 84)
                    tableWidget.setColumnWidth(13, 84)
                    tableWidget.setColumnWidth(14, 84)
            return tableWidget

        self.ui.icon_main    = QIcon(f'{ICON_PATH}/window_title.png')
        self.ui.icon_set     = QIcon(f'{ICON_PATH}/set.png')
        self.ui.icon_log     = QIcon(f'{ICON_PATH}/log.png')
        self.ui.icon_log2    = QIcon(f'{ICON_PATH}/log2.png')
        self.ui.icon_start   = QIcon(f'{ICON_PATH}/start.png')
        self.ui.icon_zoom    = QIcon(f'{ICON_PATH}/zoom.png')

        self.ui.setFont(qfont12)
        self.ui.setWindowTitle('LS 증권 옵션 자동 매매')
        self.ui.setWindowIcon(self.ui.icon_main)
        self.ui.geometry().center()

        self.ui.main_tabWidget = TabWidget(self.ui)
        self.ui.lg_tab = QWidget()
        self.ui.sj_tab = QWidget()
        self.ui.main_tabWidget.currentChanged.connect(self.ui.TabChanged)

        self.ui.main_tabWidget.addTab(self.ui.sj_tab, '')
        self.ui.main_tabWidget.addTab(self.ui.lg_tab, '')
        
        self.ui.main_tabWidget.setTabIcon(1, self.ui.icon_log)
        self.ui.main_tabWidget.setTabIcon(0, self.ui.icon_set)
        self.ui.main_tabWidget.setTabToolTip(1, '로그')
        self.ui.main_tabWidget.setTabToolTip(0, '설정')
        
        self.ui.set_tapWidgett_01 = QTabWidget(self.ui.sj_tab)
        self.ui.sj_log_labelll_00 = QLabel('', self.ui.sj_tab)
        self.ui.sj_log_labelll_00.setAlignment(Qt.AlignCenter)
        
        self.ui.sj_set_labelll_01 = QLabel('설정 관리: ', self.ui.sj_tab)
        self.ui.sj_set_pButton_01 = setPushbutton('로딩', box=self.ui.sj_tab, click=self.ui.sjButtonClicked_30)
        self.ui.sj_set_pButton_02 = setPushbutton('저장', box=self.ui.sj_tab, click=self.ui.sjButtonClicked_31)     
        
        self.ui.zo_pushButton = setPushbutton('', click=self.ui.mnButtonClicked_03, icon=self.ui.icon_zoom, shortcut='Alt+Z', tip='축소확대')
        
        self.ui.scj_tableWidget = setTablewidget(self.ui.lg_tab, columns_tg, 1)
        self.ui.sjg_tableWidget = setTablewidget(self.ui.lg_tab, columns_jg, 3, clicked=self.ui.CellClicked_02) 
        self.ui.st_textEdit  = setTextEdit(self.ui.lg_tab, vscroll=True)
        self.ui.svc_textEdit = setTextEdit(self.ui.lg_tab, vscroll=True)

        self.ui.stock_basic_list = [ 
            self.ui.scj_tableWidget,
            self.ui.sjg_tableWidget 
        ]
        
        self.ui.sj_groupBox_01 = QGroupBox(' 옵션 실서버 및 모의 서버 설정함 / 옵션전략 선택함.', self.ui.sj_tab)
        self.ui.sj_groupBox_03 = QGroupBox(' 옵션 전략 매매시간: 매매시간를 설정함.', self.ui.sj_tab)
        self.ui.sj_groupBox_04 = QGroupBox(' 옵션 전략 변수: 전략일반 변수를 설정함.', self.ui.sj_tab)
        self.ui.sj_groupBox_07 = QGroupBox(' 옵션 지표 변수: 전략지표 변수를 설정함.', self.ui.sj_tab)
        self.ui.sj_groupBox_05 = QGroupBox(' 옵션 전략 청산시스템 : 익절, 손절매를 설정함.', self.ui.sj_tab)
        self.ui.sj_groupBox_06 = QGroupBox(' 옵션 장중 시작', self.ui.sj_tab)
        self.ui.sj_textEdit    = setTextEdit(self.ui.sj_tab, vscroll=True)

        self.ui.sj_main_comboBox_01 = setCombobox(self.ui.sj_groupBox_01, tip='사용할 증권사.', items=['LS증권'])
        self.ui.sj_main_checkBox_01 = setCheckBox('모의투자서버', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_01, checked=True if self.demo_market == "1" else False)
        self.ui.sj_main_checkBox_02 = setCheckBox('실서버', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_02, checked=True if self.real_market == "1" else False)
        self.ui.sj_main_checkBox_03 = setCheckBox('텔레그램사용', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_03, checked=True if self.telegram =="1" else False)
        self.ui.sj_main_comboBox_02 = setCombobox(self.ui.sj_groupBox_01, tip='실시간 지표 사용 여부', items=['실시간 지표 사용'])
        self.ui.sj_main_checkBox_04 = setCheckBox('실시간지표', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_04, checked=True if self.realMinStrategy =="1" else False)
        self.ui.sj_main_checkBox_05 = setCheckBox('분봉지표완성', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_05, checked=True if self.minStrategy =="1" else False)        
        self.ui.sj_main_comboBox_03 = setCombobox(self.ui.sj_groupBox_01, tip='옵션 만기일 종목', items=['옵션 만기일 선택'])
        self.ui.sj_main_checkBox_06 = setCheckBox('월요일만기', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_06, checked=True if self.MON =="1" else False)
        self.ui.sj_main_checkBox_07 = setCheckBox('목요일만기', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_07, checked=True if self.THU  =="1" else False)
        self.ui.sj_main_checkBox_08 = setCheckBox('월몰만기', self.ui.sj_groupBox_01, changed=self.ui.CheckboxChanged_08, checked=True if self.MoOption =="1" else False)
                       
        text = '     전략시작시간                      전략종료시간                       청산시간'
        self.ui.sj_options_labellll_01 = QLabel(text, self.ui.sj_groupBox_03)
        self.ui.sj_options_lineEdit_01 = setLineedit(self.ui.sj_groupBox_03, ltext=self.strategy_begintime)
        self.ui.sj_options_lineEdit_02 = setLineedit(self.ui.sj_groupBox_03, ltext=self.strategy_endtime)
        self.ui.sj_options_lineEdit_03 = setLineedit(self.ui.sj_groupBox_03, ltext=self.selltime)

        text = '     리시버시작시간                  트레이더시작시간                  시스템종료시간                  ' 
        self.ui.sj_options_labellll_02 = QLabel(text, self.ui.sj_groupBox_03)
        self.ui.sj_options_lineEdit_06 = setLineedit(self.ui.sj_groupBox_03, ltext=self.receiverTime)
        self.ui.sj_options_lineEdit_07 = setLineedit(self.ui.sj_groupBox_03, ltext=self.traderTime)
        self.ui.sj_options_lineEdit_08 = setLineedit(self.ui.sj_groupBox_03, ltext=self.exitTime)
        
        text = '     옵션최소가                     옵션최대가                    투자금(천원)                      분봉                  매매종목리셋'
        self.ui.sj_allvar_labellll_01 = QLabel(text, self.ui.sj_groupBox_04)
        self.ui.sj_allvar_lineEdit_01 = setLineedit(self.ui.sj_groupBox_04, ltext=self.priceMin)
        self.ui.sj_allvar_lineEdit_02 = setLineedit(self.ui.sj_groupBox_04, ltext= self.priceMax )
        self.ui.sj_allvar_lineEdit_03 = setLineedit(self.ui.sj_groupBox_04, ltext=self.JangChoInvest)
        self.ui.sj_allvar_lineEdit_04 = setLineedit(self.ui.sj_groupBox_04, ltext=self.Bong)
        self.ui.sj_allvar_lineEdit_05 = setLineedit(self.ui.sj_groupBox_04, ltext=self.ResetTr)

        text = '      Stoch1                     Stoch2                       Stoch3                       Stoch_Level'
        self.ui.sj_allvar2_labellll_01 = QLabel(text, self.ui.sj_groupBox_07)
        self.ui.sj_allvar2_lineEdit_01 = setLineedit(self.ui.sj_groupBox_07, ltext=self.stoch_period1)
        self.ui.sj_allvar2_lineEdit_02 = setLineedit(self.ui.sj_groupBox_07, ltext=self.stoch_period2)
        self.ui.sj_allvar2_lineEdit_03 = setLineedit(self.ui.sj_groupBox_07, ltext=self.stoch_period3)  
        self.ui.sj_allvar2_lineEdit_04 = setLineedit(self.ui.sj_groupBox_07, ltext=self.stoch_level)  
        self.ui.sj_allvar2_lineEdit_05 = setCheckBox('ADX매도사용', self.ui.sj_groupBox_07, changed=self.ui.CheckboxChanged_09, checked=True if self.sellCondUse =="1" else False)         
        
        text = '      CCI_Period                CCI_Level                      sCCI_Period                    sCCI_Level'       
        self.ui.sj_allvar2_labellll_02 = QLabel(text, self.ui.sj_groupBox_07)
        self.ui.sj_allvar2_lineEdit_08 = setLineedit(self.ui.sj_groupBox_07, ltext=self.cci_period)
        self.ui.sj_allvar2_lineEdit_09 = setLineedit(self.ui.sj_groupBox_07, ltext=self.cci_level) 
        self.ui.sj_allvar2_lineEdit_10 = setLineedit(self.ui.sj_groupBox_07, ltext=self.scci_period)
        self.ui.sj_allvar2_lineEdit_11 = setLineedit(self.ui.sj_groupBox_07, ltext=self.scci_level) 

        text = '      DMI_Period                ADX_Period                    ADX_Level                       Sell_ADX_Level'       
        self.ui.sj_allvar2_labellll_03 = QLabel(text, self.ui.sj_groupBox_07) 
        self.ui.sj_allvar2_lineEdit_15 = setLineedit(self.ui.sj_groupBox_07, ltext=self.dmi_period)
        self.ui.sj_allvar2_lineEdit_16 = setLineedit(self.ui.sj_groupBox_07, ltext=self.adx_period)
        self.ui.sj_allvar2_lineEdit_17 = setLineedit(self.ui.sj_groupBox_07, ltext=self.adx_level)
        self.ui.sj_allvar2_lineEdit_18 = setLineedit(self.ui.sj_groupBox_07, ltext=self.sell_adx_level)

        text = '       손절매(%)                     익절(%)'
        self.ui.sj_ptsl_labellll_01 = QLabel(text, self.ui.sj_groupBox_05) 
        self.ui.sj_ptsl_lineEdit_01 = setLineedit(self.ui.sj_groupBox_05, ltext=self.stoploss)
        self.ui.sj_ptsl_lineEdit_02 = setLineedit(self.ui.sj_groupBox_05, ltext=self.absProfitTarget)

        self.ui.sj_stopbegin_pushButton_07 = setPushbutton('전략 시작', box=self.ui.sj_groupBox_06, click=self.ui.sjButtonClicked_07, color=1, tip='장중 전략을 시작함.')
        self.ui.sj_stopbegin_pushButton_18 = QLabel('암호: ', self.ui.sj_groupBox_06) 
        self.ui.sj_stopbegin_pushButton_19 = setLineedit(self.ui.sj_groupBox_06)
        self.ui.sj_stopbegin_pushButton_19.setEchoMode(QLineEdit.Password)

        self.ui.setFixedSize(860, 540)
        self.ui.main_tabWidget.setGeometry(5, 5, 850, 530)
        self.ui.zo_pushButton.setGeometry(5, 86, 35, 32)

        self.ui.scj_tableWidget.setGeometry(5, 5, 600, 42)
        self.ui.sjg_tableWidget.setGeometry(5, 52, 800, 110)

        self.ui.st_textEdit.setGeometry(5, 168, 320, 350)
        self.ui.svc_textEdit.setGeometry(330, 168, 475, 350)

        self.ui.sj_groupBox_01.setGeometry(15, 15, 778, 80)
        self.ui.sj_groupBox_03.setGeometry(15, 105, 778, 80)
        self.ui.sj_groupBox_04.setGeometry(15, 195, 778, 55)
        self.ui.sj_groupBox_07.setGeometry(15, 260, 778, 100)
        self.ui.sj_groupBox_05.setGeometry(15, 370, 778, 55)
        self.ui.sj_groupBox_06.setGeometry(15, 440, 778, 80)
        self.ui.sj_textEdit.setGeometry(15, 690, 778, 50)

        self.ui.sj_main_comboBox_01.setGeometry(10, 25, 130, 22)
        self.ui.sj_main_checkBox_01.setGeometry(10, 55, 120, 20)
        self.ui.sj_main_checkBox_02.setGeometry(110, 55, 90, 20)
        self.ui.sj_main_checkBox_03.setGeometry(680, 25, 100, 22) 
        self.ui.sj_main_comboBox_02.setGeometry(505, 25, 130, 22)      
        self.ui.sj_main_checkBox_04.setGeometry(600, 55, 130, 20)
        self.ui.sj_main_checkBox_05.setGeometry(505, 55, 100, 20)
        self.ui.sj_main_comboBox_03.setGeometry(210, 25, 130, 22) 
        self.ui.sj_main_checkBox_06.setGeometry(210, 55, 80, 20)
        self.ui.sj_main_checkBox_07.setGeometry(300, 55, 80, 20)
        self.ui.sj_main_checkBox_08.setGeometry(390, 55, 80, 20)      
        
        self.ui.sj_options_labellll_01.setGeometry(10, 24, 800, 20)
        self.ui.sj_options_lineEdit_01.setGeometry(110, 24, 50, 20)
        self.ui.sj_options_lineEdit_02.setGeometry(270, 24, 50, 20)
        self.ui.sj_options_lineEdit_03.setGeometry(415, 24, 50, 20)

        self.ui.sj_options_labellll_02.setGeometry(10, 55, 800, 20)
        self.ui.sj_options_lineEdit_06.setGeometry(120, 55, 50, 20)
        self.ui.sj_options_lineEdit_07.setGeometry(290, 55, 50, 20)
        self.ui.sj_options_lineEdit_08.setGeometry(445, 55, 50, 20)     
        
        self.ui.sj_allvar_labellll_01.setGeometry(10, 24, 800, 20)
        self.ui.sj_allvar_lineEdit_01.setGeometry(100, 24, 35, 20)
        self.ui.sj_allvar_lineEdit_02.setGeometry(242, 24, 35, 20)
        self.ui.sj_allvar_lineEdit_03.setGeometry(390, 24, 55, 20)
        self.ui.sj_allvar_lineEdit_04.setGeometry(505, 24, 30, 20)
        self.ui.sj_allvar_lineEdit_05.setGeometry(648, 24, 30, 20)
                
        self.ui.sj_allvar2_labellll_01.setGeometry(10, 24, 800, 20)
        self.ui.sj_allvar2_lineEdit_01.setGeometry(80, 24, 30, 20)
        self.ui.sj_allvar2_lineEdit_02.setGeometry(205, 24, 30, 20)
        self.ui.sj_allvar2_lineEdit_03.setGeometry(330, 24, 30, 20)
        self.ui.sj_allvar2_lineEdit_04.setGeometry(490, 24, 30, 20)
        self.ui.sj_allvar2_lineEdit_05.setGeometry(600, 24, 130, 20)
                
        self.ui.sj_allvar2_labellll_02.setGeometry(10, 48, 800, 20)
        self.ui.sj_allvar2_lineEdit_08.setGeometry(105, 48, 30, 20)
        self.ui.sj_allvar2_lineEdit_09.setGeometry(225, 48, 30, 20)
        self.ui.sj_allvar2_lineEdit_10.setGeometry(383, 48, 30, 20)
        self.ui.sj_allvar2_lineEdit_11.setGeometry(530, 48, 30, 20)

        self.ui.sj_allvar2_labellll_03.setGeometry(10, 72, 800, 20)
        self.ui.sj_allvar2_lineEdit_15.setGeometry(105, 72, 30, 20)
        self.ui.sj_allvar2_lineEdit_16.setGeometry(235, 72, 30, 20)
        self.ui.sj_allvar2_lineEdit_17.setGeometry(375, 72, 30, 20)
        self.ui.sj_allvar2_lineEdit_18.setGeometry(560, 72, 30, 20)
        
        self.ui.sj_ptsl_labellll_01.setGeometry(15, 24, 800, 20)
        self.ui.sj_ptsl_lineEdit_01.setGeometry(105, 24, 50, 20)
        self.ui.sj_ptsl_lineEdit_02.setGeometry(235, 24, 50, 20)

        self.ui.sj_stopbegin_pushButton_07.setGeometry(15, 30, 165, 45)
        self.ui.sj_stopbegin_pushButton_18.setGeometry(630, 35, 30, 30)
        self.ui.sj_stopbegin_pushButton_19.setGeometry(670, 35, 100, 30)

        self.ui.sj_log_labelll_00.setGeometry(335, 5, 200, 20)
        self.ui.sj_set_labelll_01.setGeometry(570, 5, 70, 20)
        self.ui.sj_set_pButton_01.setGeometry(650, 5, 70, 20)
        self.ui.sj_set_pButton_02.setGeometry(725, 5, 70, 20)
        
