import sys
from option.xingAPI import *
from utils.util import *
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication
from utils.config import *
from utils.configparserINI import Config<PERSON><PERSON><PERSON><PERSON>
from PyQt5.QtTest import *
from datetime import timedelta


class Updater(QThread):
    signal = pyqtSignal(list)
    
    def __init__(self, receiverQ):
        super().__init__()
        self.receiverQ = receiverQ

    def run(self):
        while True:
            data = self.receiverQ.get()
            if type(data) == list:
                self.signal.emit(data)

class Receiver:
    def __init__(self, qlist):
        """
            0         1           2 
        [windowQ, receiverQ, traderQ]
        """
        app_ = QApplication(sys.argv)
        
        self.windowQ            = qlist[0]
        self.receiverQ          = qlist[1]
        self.traderQ            = qlist[2]
        self.CallStrQ           = qlist[3]
        self.PutStrQ            = qlist[4]
        self.teleQ              = qlist[5]

        self.dict_set = DICT_SET
        
        self.INI = ConfigparserINI()
        self.real_market = int(self.INI.real_market)
        if self.real_market: self.Server = 1
        self.demo_market = int(self.INI.demo_market)
        if self.demo_market: self.Server = 0

        self.Bong = int(self.INI.Bong)
        self.THU = int(self.INI.THU)
        self.MON = int(self.INI.MON)
        self.MoOption = int(self.INI.MoOption)
        
        self.priceMin = float(self.INI.priceMin)
        self.priceMax = float(self.INI.priceMax)
        
        self.BongRequest = False

        self.str_tday   = strf_time('%Y%m%d')       
        self.str_year   = strf_time('%Y')     
        self.dict_bool = {
            '리시버시작': False,
	        '옵션종목선택':False,
            '옵션종목요청':False,
            '시초전략시작': False,
            '장중전략시작': False,
            '잔고청산' : False,
            '전략종료': False,
            '프로세스종료': False
        }

        self.dict_time = {
            '리시버연산시간': now(),
            '요청시간': now()
        }

        self.callCodeRequest = False
        self.trader = False
        self.call_code = ""
        self.put_code = ""
        self.code = ""
        self.dict_tick = {}
        self.dict_hgdt = {}
        self.real_data = {}

        self.list_jang     = []
        self.int_time = int(strf_time('%H%M%S'))

        self.xas = XASession()

        self.xar_op = XAReal(self)
        self.xar_ho = XAReal(self)
        self.xar_co = XAReal(self) 

        self.xar_op.RegisterRes('JIF')
        self.xar_ho.RegisterRes('OH0')
        self.xar_co.RegisterRes('OC0')   
        
        year = int(strf_time("%Y")) 
        month = int(strf_time("%m"))
        day = int(strf_time("%d")) 
        
        if self.THU:
            self.results, date = self.get_weekT_label(year, month, day, holidays_T) 
            # print("목요일 만기일: ", f"{date.year}년 {date.month}월 {date.day}일")
        elif self.MON:
            self.results, date = self.get_weekM_label(year, month, day, holidays_M) 
            # print("월요일 만기일: ", f"{date.year}년 {date.month}월 {date.day}일")
        
        self.XingLogin()
        
        self.df_jg = pd.DataFrame(columns=columns_jg)              

        self.updater = Updater(self.receiverQ)
        self.updater.signal.connect(self.UpdateJango)
        self.updater.start()

        self.qtimer = QTimer()
        self.qtimer.setInterval(1 * 250)
        self.qtimer.timeout.connect(self.Scheduler)
        self.qtimer.start()

        app_.exec_()
    
    def XingLogin(self):
        self.xas.Login(self.Server,self.INI.ID, self.INI.PW, self.INI.CERTPW)  
       
        self.windowQ.put([ui_num['로그텍스트'], '시스템 명령 실행 - XingAPI 리시버 로그인'])
        self.teleQ.put(f'XingAPI 리시버 로그인 완료')

    def UpdateJango(self, data):
        gubun, code = data
        if gubun =='콜종목코드불러오기':
            self._handle_code_request(gubun)
        elif gubun == '분봉불러오기':
            if not self.BongRequest:
                for code in [self.call_code, self.put_code]:
                    qtest_qwait(1000) 
                    self.getMinBong(code, self.Bong)  
                self.BongRequest = False                                   
        if gubun == '잔고편입' and code not in self.list_jang:
            self.code = code
            self.list_jang.append(code)
        elif gubun == '잔고청산' and code in self.list_jang:
            self.list_jang.remove(code)
        elif gubun == '전략중지':
            self.RemoveAllRealreg()
            self.call_code = ""
            self.put_code = ""
        elif gubun == '트레이더시작':
            self.trader = code
        elif gubun == '잔고' and type(code) == pd.DataFrame:
            self.df_jg = code
    
    def _handle_code_request(self, gubun):
        if gubun == '콜종목코드불러오기':
            self.callCodeRequest = True
        
        self.BongRequest = True
        self.dict_bool['옵션종목선택'] = False
        self.dict_bool['옵션종목요청'] = False
        self.dict_bool['리시버시작'] = False

    def Scheduler(self):
        # 오늘이 주말인지 확인 (5=토요일, 6=일요일)
        today_weekday = datetime.datetime.now().weekday()
        is_weekend = today_weekday >= 5  # 토요일 또는 일요일
        current_time = float(strf_time('%H%M%S.%f'))
        
        # 첫 번째 조건: 장 시작 전 옵션 종목 설정
        if self.int_time < 84501.5 <= current_time and not self.dict_bool['옵션종목선택'] and not self.dict_set['리시버']:
            self.dict_set['리시버'] = True
            self.call_code, self.put_code = self.getOptionTradingCode()
            QTimer.singleShot(3400,  self.OperationRealreg)          
        
        # 두 번째 조건: 장 시작 후 리시버 시작 안된 경우
        elif 84503 < current_time and not self.dict_bool['리시버시작']:
            self.dict_bool['리시버시작'] = True
            if not self.dict_bool['옵션종목선택']:          
                self.call_code, self.put_code = self.getOptionTradingCode()

            if not self.dict_bool['옵션종목요청'] and not is_weekend: 
                for code in [self.call_code, self.put_code]:
                    self.getMinBong(code, self.Bong)
                    qtest_qwait(1000) 
                qtest_qwait(1000) 
                self.dict_bool['옵션종목요청'] = True
            
            QTimer.singleShot(3400,  self.OperationRealreg)
        
        # 세 번째 조건: 코드 요청 처리
        elif self.callCodeRequest and not self.dict_bool['리시버시작']:
            self._handle_specific_code_request()
        
        # 장 마감 시간 체크
        if 164600 <= int(strf_time('%H%M%S')):
            self.SysExit()
            
        self.int_time = int(strf_time('%H%M%S'))

    def _setup_option_codes(self):
        """옵션 코드를 설정하고 관련 큐에 전송"""
        # 코드 요청 타입에 따른 처리
        if self.callCodeRequest:
            self.callCodeRequest = False
            self.call_code, self.put_code = self.getOptionTradingCode()         

    def _request_min_data(self):
        """분봉 데이터 요청"""
        for code in [self.call_code, self.put_code]:
            self.getMinBong(code, self.Bong)
            qtest_qwait(1000)
        qtest_qwait(1000)

    def _handle_specific_code_request(self):
        """특정 코드 요청 처리"""
        # 이전 리얼 데이터 제거
        self.RemoveAllRealreg()
        
        if self.callCodeRequest:
            self.callCodeRequest = False
        
        # 옵션 코드 설정 및 분봉 데이터 요청
        if not self.dict_bool['옵션종목선택']:
            self._setup_option_codes()
        
        if not self.dict_bool['옵션종목요청']:
            self._request_min_data()
            self.dict_bool['옵션종목요청'] = True
        
        # 타이머 설정
        QTimer.singleShot(400, self.OperationRealreg)
        QTimer.singleShot(1000*45, self.BongRequestF)

    def BongRequestF(self):      
        self.BongRequest = False
        
    def getOptionTradingCode(self):
        self.dict_bool['옵션종목선택'] = True
        
        if self.THU or self.MON:
            df_info = getKoreanOptionInfo(self.results, "W")

            if self.THU:
                df_info_c = df_info[df_info['옵션코드'].str.contains('209')]
                df_info_p = df_info[df_info['옵션코드'].str.contains('309')] 
            elif self.MON:
                df_info_c = df_info[df_info['옵션코드'].str.contains('2AF')]
                df_info_p = df_info[df_info['옵션코드'].str.contains('3AF')] 
                
        elif self.MoOption:
            int_m = int(strf_time("%Y%m%d")[4:6])
            Year =""
            
            if int(OptFutD[strf_time("%m")]) < int(strf_time("%d")):
                if int_m + 1 > 12:
                    int_m = 1
                    Year = str(int(strf_time("%Y")) + 1)
                else:
                    int_m = int_m + 1
                    Year = strf_time("%Y")
            else:
                int_m = int_m
                Year = strf_time("%Y")
            if int_m <= 9:
                int_mon = Year + "0" + str(int_m)
            else:
                int_mon = Year + str(int_m)
            # print("옵션 월: ", int_mon)

            df_info = getKoreanOptionInfo(int_mon, "G")
            # print("옵션 Type: ", df_info)
   
            df_info_c = df_info[df_info['옵션코드'].str.contains('201'+ OptFutY[Year] + OptFutM[str(int_m)])]
            df_info_p = df_info[df_info['옵션코드'].str.contains('301'+ OptFutY[Year] + OptFutM[str(int_m)])]

        list_optcode_c = df_info_c.loc[:, '옵션코드'] # 전 종목 가져오기
        list_optcode_p = df_info_p.loc[:, '옵션코드'] # 전 종목 가져오기  
        
        list_optprice_c = [float(i.strip()) for i in df_info_c.loc[:, '현재가']]
        list_optprice_p = [float(i.strip()) for i in df_info_p.loc[:, '현재가']]
        
        optiondic_c = {key: value for key, value in zip(list_optcode_c, list_optprice_c)}
        option_dic_c = {key : value for key, value in optiondic_c.items() if  self.priceMin <= value < self.priceMax}
        num1 = 0
        while option_dic_c == {}:
            option_dic_c = {key : value for key, value in optiondic_c.items() if self.priceMin <= value < self.priceMax + num1}
            num1 += 0.1
        
        # 옵션을 찾지 못한 경우 처리
        if option_dic_c == {} and self.call_code != "":
            # 범위를 확장해서 최소 하나는 선택되도록 함
            option_dic_c = {self.call_code: 1}
        elif option_dic_c == {} and self.call_code == "":
            option_dic_c = {key : value for key, value in optiondic_c.items() if  self.priceMin <= value < 3 }
        
        optiondic_p = {key: value for key, value in zip(list_optcode_p, list_optprice_p)}
        option_dic_p = {key : value for key, value in optiondic_p.items() if  self.priceMin <= value < self.priceMax}
        num2 = 0
        while option_dic_p == {} and num2 < 1:
            option_dic_p = {key : value for key, value in optiondic_p.items() if  self.priceMin <= value < self.priceMax + num2}
            num2 += 0.1
        
        # 옵션을 찾지 못한 경우 처리
        if option_dic_p == {} and self.put_code != "":
            # 범위를 확장해서 최소 하나는 선택되도록 함
            option_dic_p = {self.put_code: 1}
        elif option_dic_p == {} and self.put_code == "":
            option_dic_p = {key : value for key, value in optiondic_p.items() if  self.priceMin <= value < 3}
            
        call_code = min(option_dic_c, key=option_dic_c.get)
        put_code = min(option_dic_p, key=option_dic_p.get)
        
        # if len(self.df_jg) >= 1:
        #     for code in self.df_jg.index:
        #         if ('309' in code or '3AF' in code or '301' in code):
        #             self.put_code = code
        #         elif ('209' in code or '2AF' in code or '201' in code):
        #             self.call_code = code 
        # self.windowQ.put([ui_num['단순텍스트'], f'선택된 종목코드/가격 - 콜옵션:: {option_dic_c}, 풋옵션:: {option_dic_p}'])
        self.teleQ.put(f'선택된 종목코드/가격 - 콜옵션:: {option_dic_c}, 풋옵션:: {option_dic_p}')

        return call_code, put_code

    def OperationRealreg(self):
        self.dict_bool['리시버시작'] = True       
        if len(self.df_jg) >= 1:
            for code in self.df_jg.index:
                if ('309' in code or '3AF' in code or '301' in code):
                    self.put_code = code
                elif ('209' in code or '2AF' in code or '201' in code):
                    self.call_code = code  
        codes = [self.call_code, self.put_code]     
        self.xar_op.AddRealData('0')
        for code in codes:
            self.xar_co.AddRealData(code)
            self.xar_ho.AddRealData(code)
        
        # 큐에 코드 정보 전송
        self.CallStrQ.put(["거래할콜옵션", self.call_code])
        self.PutStrQ.put(["거래할풋옵션", self.put_code])
        self.traderQ.put(["거래할콜옵션", self.call_code])
        self.traderQ.put(["거래할풋옵션", self.put_code])
        self.windowQ.put([ui_num['단순텍스트'], f'거래할 옵션:: 콜옵션: {self.call_code}, 풋옵션: {self.put_code}'])
        self.teleQ.put(f'선택된 종목코드/가격 - 콜옵션:: {self.call_code}, 풋옵션: {self.put_code}')
        
    def SysExit(self):
        self.dict_bool['프로세스종료'] = True
        self.qtimer.stop()
        self.updater.quit()
        self.RemoveAllRealreg()
        self.windowQ.put([ui_num['로그텍스트'], '시스템 명령 실행 - 리시버 종료'])
            
    def RemoveAllRealreg(self):
        self.xar_co.RemoveAllRealData()
        self.xar_ho.RemoveAllRealData()
        self.windowQ.put([ui_num['로그텍스트'], '시스템 명령 실행 - 실시간 데이터 중단 완료'])
                   
    def OnReceiveRealData(self, data):      
        if int(strf_time('%H%M%S')) < 83500:
            return
        try:
            optcode = data['optcode']
            self.sstime   = int(data['chetime'])
            self.cur    = data['price']
            self.vol    = int(data['cvolume'])
            o    = data['open']
            h    = data['high']
            low  = data['low']

            dt   = int(self.str_tday + data['chetime'])
        except Exception as e:
            self.windowQ.put([ui_num['로그텍스트'], f'시스템 명령 오류 - OnReceiveRealData {e}'])
        else:
            self.dict_tick[optcode] = [self.sstime, self.cur, self.vol]     
            self.UpdateTickData(optcode)    

        if self.trader and optcode in self.list_jang:
           self.traderQ.put(["현재가", optcode, self.cur])         
                        
    def OnReceiveHogaData(self, data):
        if int(strf_time('%H%M%S')) < 83500:
            return
        try:
            start = data['receivetime']
            optcode = data['optcode']
            hogatime =   data['hotime']
            dt = int(self.str_tday + str(int(data['hotime'])))

        except Exception as e:
            self.windowQ.put([ui_num['시스템 명령 오류'], f'시스템 명령 오류 - OnReceiveHogaData 호가잔량 {e}'])
        else:
            self.dict_hgdt[optcode] = [hogatime, optcode]
                    
    def UpdateTickData(self, optcode):   
        if self.trader:    
            if optcode in self.dict_tick.keys():
                self.real_data[optcode] = self.dict_tick[optcode] + [optcode]
                if self.call_code == optcode:
                    self.CallStrQ.put(self.real_data[optcode])                                
                elif self.put_code  == optcode:
                    self.PutStrQ.put(self.real_data[optcode]) 
            
    def getMinBong(self, optcode, minBong):
        dict_tick_ar = {}
        try:
            df = getOption8415(optcode, minBong)
            columns = df.columns.tolist()
            df[columns[1:]] = df[columns[1:]].astype('float64')  # '체결시간' 열을 제외한 나머지 열을 float64로 변환
            df[columns[0]] = df[columns[0]].astype('int64')      # '체결시간' 열은 int64로 변환
            df = df.abs()
            dict_tick_ar[optcode] = df.to_numpy()
            if len(dict_tick_ar[optcode]) > 1:
                self.dict_bool['옵션종목요청'] = True
                if "209" in optcode or "2AF" in optcode or "201" in optcode:
                    self.CallStrQ.put(["콜분봉받기", optcode, dict_tick_ar[optcode]])
                elif "309" in optcode or "3AF" in optcode or "301" in optcode:
                    self.PutStrQ.put(["풋분봉받기", optcode, dict_tick_ar[optcode]])
        except:
            pass
        
    def get_weekT_label(self, year, month, day, holidays):
        date = datetime.date(year, month, day)
        first_day = datetime.date(year, month, 1)
        last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        # 해당 월의 첫 번째 목요일 찾기
        first_thursday = first_day + timedelta(days=(3 - first_day.weekday() + 7) % 7)

        # 주어진 날짜가 속한 주의 목요일 찾기
        current_thursday = date + timedelta(days=(3 - date.weekday() + 7) % 7)
        
        # 주차 계산
        week_number = (current_thursday - first_thursday).days // 7 + 1

        # end_date 설정
        end_date = current_thursday

        # 공휴일 체크 및 처리
        holiday_key = f"{end_date.month:02d}{end_date.day:02d}"
        if holiday_key in holidays.get(str(year), set()):
            if end_date.weekday() == 3:  # 목요일
                end_date -= timedelta(days=1)  # 수요일로 조정

        # 만약 end_date가 다음 달이면 week_number를 1로 설정
        if end_date.month != month:
            week_number = 1
            # 다음 달의 첫 번째 목요일 찾기
            next_month = (date.replace(day=28) + timedelta(days=5)).replace(day=1)
            end_date = next_month + timedelta(days=(3 - next_month.weekday() + 7) % 7)

        return f"W{week_number}THU", end_date
    
    def get_weekM_label(self, year, month, day, holidays):
        date = datetime.date(year, month, day)
        
        # 오늘이 공휴일인지 직접 확인
        is_today_holiday = f"{date.month:02d}{date.day:02d}" in holidays.get(str(date.year), set())
        
        # 오늘이 공휴일이면, 다음 평일을 만기일로 설정
        if is_today_holiday:
            # 다음 날짜부터 공휴일이 아닌 날 찾기
            expiry_date = date + timedelta(days=1)
            while f"{expiry_date.month:02d}{expiry_date.day:02d}" in holidays.get(str(expiry_date.year), set()):
                expiry_date += timedelta(days=1)
                
            # 만기일 설정
            # print(f"오늘({date.year}-{date.month:02d}-{date.day:02d})은 공휴일이므로 만기일은 {expiry_date.year}-{expiry_date.month:02d}-{expiry_date.day:02d}입니다.")
            
            # 주차 계산 (원래 월요일 기준)
            # 이번 주 월요일 찾기
            days_since_monday = date.weekday()  # 월요일부터 며칠 지났는지 (0=월, 1=화, ...)
            this_monday = date - timedelta(days=days_since_monday)  # 이번 주 월요일
            
            # 해당 월의 첫 번째 월요일 찾기
            first_day_of_month = this_monday.replace(day=1)
            first_monday = first_day_of_month + timedelta(days=(7 - first_day_of_month.weekday()) % 7)
            
            # 주차 계산
            week_number = (this_monday - first_monday).days // 7 + 1
            
            return f"W{week_number}MON", expiry_date
            
        # 오늘이 공휴일이 아니면, 월요일 기준 로직 적용
        
        # 1. 현재 주의 월요일 찾기 (오늘이 월요일이면 오늘, 아니면 지난 월요일)
        days_since_monday = date.weekday()  # 월요일부터 며칠 지났는지 (0=월, 1=화, ...)
        this_monday = date - timedelta(days=days_since_monday)  # 이번 주 월요일
        
        # 2. 이번 주 월요일이 원래 만기일인지 확인 (만기일 = 월요일)
        original_expiry = this_monday
        adjusted_expiry = original_expiry
        
        # 3. 만기일이 공휴일인 경우 처리 (연속된 공휴일을 모두 건너뜀)
        is_monday_holiday = False
        if f"{adjusted_expiry.month:02d}{adjusted_expiry.day:02d}" in holidays.get(str(adjusted_expiry.year), set()):
            is_monday_holiday = True
            
        while f"{adjusted_expiry.month:02d}{adjusted_expiry.day:02d}" in holidays.get(str(adjusted_expiry.year), set()):
            adjusted_expiry += timedelta(days=1)
        
        # 4. 오늘이 만기일인지 확인
        is_today_expiry = False
        
        # 4-1. 오늘이 월요일이고 공휴일이 아니면 만기일
        if date.weekday() == 0 and not is_monday_holiday:
            is_today_expiry = True
            
        # 4-2. 오늘이 월요일이고 공휴일이면, 조정된 날짜를 반환
        elif date.weekday() == 0 and is_monday_holiday:
            # 주차 계산
            first_day_of_month = original_expiry.replace(day=1)
            first_monday = first_day_of_month + timedelta(days=(7 - first_day_of_month.weekday()) % 7)
            week_number = (original_expiry - first_monday).days // 7 + 1
            return f"W{week_number}MON", adjusted_expiry
            
        # 4-3. 오늘이 월요일이 아니고, 조정된 만기일이 오늘인 경우
        elif date == adjusted_expiry and is_monday_holiday:
            is_today_expiry = True
        
        # 5. 최종 만기일 설정
        if is_today_expiry:
            # 오늘이 만기일이면 오늘 반환
            expiry_date = date
            # 원래 월요일 설정 (주차 계산용)
            if date.weekday() == 0:
                original_date = date  # 오늘이 월요일
            else:
                original_date = this_monday  # 이번 주 월요일
        else:
            # 오늘이 만기일이 아니면 다음 월요일 찾기
            next_monday = this_monday + timedelta(days=7)
            expiry_date = next_monday
            original_date = next_monday  # 원래 월요일 (주차 계산용)
            
            # 만기일이 공휴일인 경우 처리
            while f"{expiry_date.month:02d}{expiry_date.day:02d}" in holidays.get(str(expiry_date.year), set()):
                # print(f"공휴일 발견: {expiry_date.year}-{expiry_date.month:02d}-{expiry_date.day:02d}")
                expiry_date += timedelta(days=1)
            
            # if original_date != expiry_date:
            #     print(f"조정된 만기일: {expiry_date.year}-{expiry_date.month:02d}-{expiry_date.day:02d}")
        
        # 해당 월의 첫 번째 월요일 찾기
        first_day_of_month = original_date.replace(day=1)
        first_monday = first_day_of_month + timedelta(days=(7 - first_day_of_month.weekday()) % 7)
        
        # 주차 계산 (원래 월요일 기준)
        week_number = (original_date - first_monday).days // 7 + 1
        
        return f"W{week_number}MON", expiry_date
        

                    


