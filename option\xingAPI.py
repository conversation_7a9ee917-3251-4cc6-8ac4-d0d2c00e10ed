import datetime
import pythoncom
import pandas as pd
import win32com.client
from utils.util import *
from utils.config import *
import logging
import os
from utils.configparserINI import ConfigparserINI
import time

INI = ConfigparserINI()
MoOption = int(INI.MoOption)
# 로깅 설정
logging.basicConfig(filename='app.log', level=logging.DEBUG, 
                    format='%(asctime)s - %(levelname)s - %(message)s')

def parse_block(data):
    block_info = data[0]
    tokens = block_info.split(",")
    block_code, block_type = tokens[0], tokens[-1][:-1]
    fields = data[2:]
    field_codes = [line.split(',')[1].strip() for line in fields if len(line) > 0]
    ret_data = {block_code: field_codes}
    return block_type, ret_data

def parseRes(lines):
    lines = [line.strip() for line in lines]
    info_index = [i for i, x in enumerate(lines) if x.startswith((".Func", ".Feed"))][0]
    begin_indices = [i - 1 for i, x in enumerate(lines) if x == "begin"]
    end_indices = [i for i, x in enumerate(lines) if x == "end"]
    block_indices = zip(begin_indices, end_indices)
    ret_data = {"trcode": None, "inblock": [], "outblock": []}
    tr_code = lines[info_index].split(',')[2].strip()
    ret_data["trcode"] = tr_code
    for start, end in block_indices:
        block_type, block_data = parse_block(lines[start:end])
        if block_type == "input":
            ret_data["inblock"].append(block_data)
        else:
            ret_data["outblock"].append(block_data)
    return ret_data


class XASession:
    def __init__(self):
        self.com_obj = win32com.client.Dispatch("XA_Session.XASession")
        win32com.client.WithEvents(self.com_obj, XASessionEvents).connect(self)
        self.connected = False
        self.last_error_code = ""
        self.last_error_msg = ""

    def Login(self, server, user_id, password, cert):
        os.chdir(r'C:\LS_SEC\xingAPI')
        if server == 0:
            print("------------- 모의투자 서버 접속 -----------------")
            self.com_obj.ConnectServer("demo.ls-sec.co.kr", 20001) 
        else: 
            print("------------- 실서버 접속 -----------------")
            self.com_obj.ConnectServer("api.ls-sec.co.kr", 20001) 
        self.com_obj.Login(user_id, password, cert, 0, 0)
        while not self.connected:
            pythoncom.PumpWaitingMessages()
    
    def connect_server(self, server_type):
        """서버에 연결합니다. 연결 성공 시 True, 실패 시 False를 반환합니다.
        
        Args:
            server_type (str): 'demo' 또는 'real'
        """
        os.chdir(r'C:\LS_SEC\xingAPI')
        try:
            if server_type.lower() == "demo":
                print("------------- 모의투자 서버 접속 -----------------")
                result = self.com_obj.ConnectServer("demo.ls-sec.co.kr", 20001)
            else:
                print("------------- 실서버 접속 -----------------")
                result = self.com_obj.ConnectServer("api.ls-sec.co.kr", 20001)
            
            # 서버 연결 결과 확인
            if result:
                return True
            else:
                self.last_error_code = self.com_obj.GetLastError()
                self.last_error_msg = self.com_obj.GetErrorMessage(self.last_error_code)
                return False
        except Exception as e:
            self.last_error_msg = str(e)
            logging.error(f"서버 연결 중 오류 발생: {self.last_error_msg}")
            return False
    
    def login(self, user_id, password, cert_pw):
        """로그인합니다. 로그인 성공 시 True, 실패 시 False를 반환합니다.
        
        Args:
            user_id (str): 사용자 ID
            password (str): 비밀번호
            cert_pw (str): 공인인증서 비밀번호
        """
        try:
            result = self.com_obj.Login(user_id, password, cert_pw, 0, 0)
            if result >= 0:
                # 로그인 요청 성공 - 이벤트 응답 대기
                timeout = 0
                while not self.connected and timeout < 30:  # 30초 타임아웃
                    pythoncom.PumpWaitingMessages()
                    time.sleep(0.1)
                    timeout += 0.1
                
                return self.connected
            else:
                self.last_error_code = self.com_obj.GetLastError()
                self.last_error_msg = self.com_obj.GetErrorMessage(self.last_error_code)
                return False
        except Exception as e:
            self.last_error_msg = str(e)
            logging.error(f"로그인 중 오류 발생: {self.last_error_msg}")
            return False
    
    def is_connected(self):
        """서버 연결 상태 확인"""
        return self.connected
    
    def get_last_error_msg(self):
        """마지막 에러 메시지 반환"""
        if self.last_error_msg:
            return self.last_error_msg
        
        if hasattr(self.com_obj, 'GetLastError'):
            err_code = self.com_obj.GetLastError()
            if err_code != 0 and hasattr(self.com_obj, 'GetErrorMessage'):
                return self.com_obj.GetErrorMessage(err_code)
        
        return "알 수 없는 오류"

    def GetAccountList(self, index):
        account = self.com_obj.GetAccountList(index)
        return account  
    
    def GetAccountListCount(self):
        acc_list = self.com_obj.GetAccountListCount()
        return acc_list
    
    def GetAcctDetailName(self, accNr):
        accName = self.com_obj.GetAcctDetailName(accNr)
        return accName
    
    def logout(self):
        self.com_obj.Logout()      
    
    def disconnect_server(self):
        self.com_obj.DisconnectServer()
        self.connected = False

class XAReal:
    def __init__(self, user_class):
        self.com_obj = win32com.client.Dispatch("XA_DataSet.XAReal")
        win32com.client.WithEvents(self.com_obj, XARealEvents).connect(self, user_class)
        self.res = {}

    def RegisterRes(self, res_name):
        res_path = E_OPENAPI_PATH + '/Res/' + res_name + '.res'
        self.com_obj.ResFileName = res_path
        with open(res_path, encoding="euc-kr") as f:
            res_lines = f.readlines()
            res_data = parseRes(res_lines)
            self.res[res_name] = res_data

    def AddRealData(self, code=None):
        if code is not None:
            if code == '0':
                self.com_obj.SetFieldData('InBlock', 'jangubun', code)
            else:
                self.com_obj.SetFieldData('InBlock', 'optcode', code)
        self.com_obj.AdviseRealData()

    def RemoveRealData(self, code):
        self.com_obj.UnadviseRealDataWithKey(code)

    def RemoveAllRealData(self):
        self.com_obj.UnadviseRealData()

    def GetFieldData(self, field):
        return self.com_obj.GetFieldData('OutBlock', field)

    def GetBlockData(self):
        return self.com_obj.GetBlockData('OutBlock')


class XASessionEvents:
    def __init__(self):
        self.com_class = None

    def connect(self, com_class):
        self.com_class = com_class

    def OnLogin(self, code, msg):
        if code == '0000':
            logging.info(f"로그인 성공: {msg}")
            self.com_class.connected = True
        else:
            logging.error(f"로그인 실패 - 코드: {code}, 메시지: {msg}")
            self.com_class.last_error_code = code
            self.com_class.last_error_msg = msg
            self.com_class.connected = False
    
    def OnLogout(self):
        logging.info("로그아웃 완료")
        self.com_class.connected = False
        print("OnLogout method is called")
    
    def OnDisconnect(self):
        logging.warning("서버와의 연결이 끊어졌습니다")
        self.com_class.connected = False
        print("OnDisconnect method is called")

class XARealEvents:
    def __init__(self):
        self.com_class = None
        self.user_class = None
        self.dict_bool = {
            '호가잔량필드확인': False,
            '호가잔량필드같음': False
        }

    def connect(self, com_class, user_class):
        self.com_class = com_class
        self.user_class = user_class

    def OnReceiveRealData(self, trcode):
        if trcode in ['JIF','O01','C01']:
            res_data = self.com_class.res[trcode]
            out_block = res_data['outblock'][0]
            out_data = {field: self.com_class.GetFieldData(field) for field in out_block['OutBlock']}

            if trcode == 'O01':
                self.user_class.OnReceiveJeobsuData(out_data)
            elif trcode == 'C01':
                self.user_class.OnReceiveChegeolData(out_data)
        else:
            if trcode in ['YOC']:
                out_data = {}
                res_data = self.com_class.res.get(trcode)
                out_block = res_data['outblock'][0]
                for field in out_block['OutBlock']: 
                    data = self.com_class.GetFieldData(field)
                    if field in ['optcode']:
                        data = data.strip()
                    elif field in ['yeprice']:
                        data = abs(float(data))
                    out_data[field] = data
                self.user_class.OnReceiveYeSangChegeolData(out_data)
            elif trcode in ['OC0']:
                out_data = {}
                res_data = self.com_class.res.get(trcode)
                out_block = res_data['outblock'][0]
                for field in out_block['OutBlock']:
                    data = self.com_class.GetFieldData(field)
                    if field in ['price', 'open', 'high', 'low', 'offerho1', 'bidho1']:
                        data = abs(float(data))
                    elif field in ['cvolume, volume']:
                        data = int(data)
                    out_data[field] = data
                self.user_class.OnReceiveRealData(out_data)
            elif trcode in ['OH0']:
                start = datetime.datetime.now()
                if not self.dict_bool['호가잔량필드확인']:
                    res_data = self.com_class.res.get(trcode)
                    out_data = {}
                    out_block = res_data['outblock'][0]
                    for field in out_block['OutBlock']:
                        data = self.com_class.GetFieldData(field)
                        if 'ho' in field:
                            data = abs(float(data))
                        elif 'rem' in field or field == 'volume':
                            data = int(data)
                        elif 'hotime' in field:
                            data = data
                        out_data[field] = data                    

                out_data['receivetime'] = start
                self.user_class.OnReceiveHogaData(out_data)

logger = logging.getLogger('Apis.Abstract') # Not will be used on normal situation.
ch = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
ch.setFormatter(formatter)
              
module_logger = logging.getLogger('Alpha.OptionTrading')
module_logger.setLevel(logging.INFO)
module_logger.addHandler(ch)

class TROccurs(object):
   
    def __init__(self):
        self.is_data_received = False
        self.df_received_data = pd.DataFrame()
        
    def OnReceiveData(self, tr_code):
        pass

    def getResultData(self):
        """
        DataFrame화 된 TR요청 결과를 반환.
        :return:
        """
        return self.df_received_data

    def start(self, code):
        """
        code must be string
        :param code:
        :return:
        """
        self.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\" + code + ".res"

    def singleRequest(self, *args):
        pass

    @classmethod
    def getInstance(cls):
        xq = win32com.client.DispatchWithEvents("XA_DataSet.XAQuery", cls)
        return xq
    

class KoreanOptionInfo(TROccurs):
    def __init__(self):
        TROccurs.__init__(self) # 추상화 클래스 생성자

        self.code = "t2301" #TR Code
        
        self.is_data_received = False
        self.count = 0

    def OnReceiveData(self, tr_code):
        self.is_data_received = True
        if MoOption:
            # print("모옵션 설정이 활성화되어 있습니다.")
            self.count = self.GetBlockCount("t2301OutBlock1")
            if self.count == 0:
                # 데이터가 없는 경우 기본 데이터프레임 생성
                # print("데이터가 없습니다. 기본 데이터프레임을 생성합니다.")
                self.df_received_data = pd.DataFrame(columns=["옵션코드", "행사가", "현재가"])
                return
        else:
            self.count = self.GetBlockCount("t2301OutBlock1")
        
        # print("count = {0}".format(self.count))
        # print("TR code ==> {0}".format(tr_code))

        self.total_data = []
        for k in range(2):
            if k == 0:
                blockName = "t2301OutBlock1"
                dictIdx = "콜"
            else:
                blockName = "t2301OutBlock2"
                dictIdx = "풋"
            
            for i in range(self.count):
                tmp_data = [
                    self.GetFieldData(blockName, "optcode", i) ,
                    self.GetFieldData(blockName, "actprice", i) ,
                    self.GetFieldData(blockName, "price", i)
                ]
                self.total_data.append(tmp_data)
        
        # 데이터가 없는 경우 기본 데이터프레임 생성
        if len(self.total_data) == 0:
            # print("수집된 데이터가 없습니다. 기본 데이터프레임을 생성합니다.")
            self.df_received_data = pd.DataFrame(columns=["옵션코드", "행사가", "현재가"])
            return
            
        df_total_data = pd.DataFrame(self.total_data)
        # print("옵션1: ", df_total_data)
        
        df_total_data.columns = [
            "옵션코드",
            "행사가",
            "현재가"
        ]
        # print(df_total_data)
        self.df_received_data = df_total_data
            
    def start(self):
        TROccurs.start(self, self.code)

    def singleRequest(self, code, Option = "G"):
        self.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\t2301.res" 
        self.SetFieldData("t2301InBlock", "yyyymm", 0, str(code) )
        self.SetFieldData("t2301InBlock", "gubun", 0, str(Option) )

        err_code = self.Request(False) # 

        if err_code < 0:
            print("error... {0}".format(err_code))
    
class Option8414(TROccurs):
    def __init__(self):
        TROccurs.__init__(self) # 추상화 클래스 생성자
        
        self.code = "t8414" #TR Code
        
        self.is_data_received = False
        self.count = 0

    def OnReceiveData(self, tr_code):
        self.is_data_received = True

        self.count = self.GetBlockCount("t8414OutBlock1")
        self.total_data = []
        
        for i in range(self.count):
            tmp_data = [
            # self.GetFieldData("t8414OutBlock1", "date", i).strip(), 
            self.GetFieldData("t8414OutBlock1", "time", i).strip(), 
            self.GetFieldData("t8414OutBlock1", "open", i),
            self.GetFieldData("t8414OutBlock1", "high", i),
            self.GetFieldData("t8414OutBlock1", "low", i),
            self.GetFieldData("t8414OutBlock1", "close", i),
            self.GetFieldData("t8414OutBlock1", "jdiff_vol", i),
            ]
                        
            self.total_data.append(tmp_data)
        if self.total_data != []:    
            df_total_data = pd.DataFrame(self.total_data)
            df_total_data.columns = [
                # "날짜",
                "체결시간",
                "틱봉시가",
                "틱봉고가",
                "틱봉저가",
                "틱봉종가", 
                "틱봉거래량",           
            ]
            self.df_received_data = df_total_data         
            
    def start(self):
        TROccurs.start(self, self.code)

    def singleRequest(self, code, ncnt):
        self.int_mon = str(int(strf_time('%Y%m%d')[0:8]))
        self.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\t8414.res" # RES 파일 등록
        self.SetFieldData("t8414InBlock", "shcode", 0, code) # 종목번호 
        self.SetFieldData("t8414InBlock", "ncnt", 0, ncnt) # 주기(분)구분(0:30초 1: 1틱 2: 2틱 ..... n: n틱)
        self.SetFieldData("t8414InBlock", "qrycnt", 0, "") #봉 수)
        self.SetFieldData("t8414InBlock", "sdate", 0, self.int_mon ) #시작일자)
        self.SetFieldData("t8414InBlock", "edate", 0, self.int_mon ) #끝일자)
        self.SetFieldData("t8414InBlock", "cts_date", 0, '') #연속일자)
        self.SetFieldData("t8414InBlock", "cts_time", 0, '') #연속시간)
        self.SetFieldData("t8414InBlock", "comp_yn", 0, 'N') #압축여부 : Y:압축, N:비압축

        err_code = self.Request(False) # data 요청하기 -- 연속조회인경우만 True

        if err_code < 0:
            print("error... {0}".format(err_code))
            
class Option8415(TROccurs):
    def __init__(self):
        TROccurs.__init__(self) # 추상화 클래스 생성자
        
        self.code = "t8415" #TR Code
        
        self.is_data_received = False
        self.count = 0

    def OnReceiveData(self, tr_code):
        self.is_data_received = True
        self.count = self.GetBlockCount("t8415OutBlock1")
        # print(self.count)
        self.total_data = []
        
        for i in range(self.count):
            tmp_data = [
            # self.GetFieldData("t8415OutBlock1", "date", i).strip(), 
            self.GetFieldData("t8415OutBlock1", "time", i).strip(), 
            self.GetFieldData("t8415OutBlock1", "open", i),
            self.GetFieldData("t8415OutBlock1", "high", i),
            self.GetFieldData("t8415OutBlock1", "low", i),
            self.GetFieldData("t8415OutBlock1", "close", i),  
            self.GetFieldData("t8415OutBlock1", "jdiff_vol", i)                      
            ]      
            
            self.total_data.append(tmp_data)
            
        df_total_data = pd.DataFrame(self.total_data)
        df_total_data.columns = [
            # "날짜",
            "시간",
            "분봉시가",
            "분봉고가",
            "분봉저가",
            "분봉종가",
            "분봉거래량"            
        ]
        self.df_received_data = df_total_data         
            
    def start(self):
        TROccurs.start(self, self.code)

    def singleRequest(self, code, ncnt):
        self.int_mon = str(int(strf_time('%Y%m%d')[0:8]))
        self.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\t8415.res" # RES 파일 등록
        self.SetFieldData("t8415InBlock", "shcode", 0, code) # 종목번호 
        self.SetFieldData("t8415InBlock", "ncnt", 0, ncnt) # 주기(분)구분(0:30초 1: 1분 2: 2분 ..... n: n분)
        self.SetFieldData("t8415InBlock", "qrycnt", 0, "") #봉 수)
        self.SetFieldData("t8415InBlock", "sdate", 0, self.int_mon ) #시작일자)
        self.SetFieldData("t8415InBlock", "edate", 0, self.int_mon ) #끝일자)
        self.SetFieldData("t8415InBlock", "cts_date", 0, '') #연속일자)
        self.SetFieldData("t8415InBlock", "cts_time", 0, '') #연속시간)
        self.SetFieldData("t8415InBlock", "comp_yn", 0, 'N') #압축여부 : Y:압축, N:비압축

        err_code = self.Request(False) # data 요청하기 -- 연속조회인경우만 True

        if err_code < 0:
            print("error... {0}".format(err_code))

class Option0441(TROccurs):
    def __init__(self):
        TROccurs.__init__(self) # 추상화 클래스 생성자
        
        self.code = "t0441" #TR Code
        
        self.is_data_received = False
        self.count = 0

    def OnReceiveData(self, tr_code):
        self.is_data_received = True
        
        self.df_total = []
        tmp_data = []
        tr_profit = self.GetFieldData("t0441OutBlock", "tdtsunik", 0) #매매손익합
        total = self.GetFieldData("t0441OutBlock", "tappamt", 0) #평가금액  # 매수/매도 구분이 없이 합임. 실제로는 매수/매도 구분하여 +-필요
        profit =  self.GetFieldData("t0441OutBlock", "tsunik", 0) #평가손익
        tmp_data = [tr_profit, total, profit]
        self.df_total.append(tmp_data)
        df_total = pd.DataFrame(self.df_total)
        df_total.columns = [
            "매매손익합",
            "평가금액",
            "평가손익",
        ]
        self.count = self.GetBlockCount("t0441OutBlock1")    
        
        self.total_data = []
        tmp_data = []
        if self.count > 0:
            for i in range(self.count):
                tmp_data = [
                self.GetFieldData("t0441OutBlock1", "expcode", i).strip(), #종목코드
                self.GetFieldData("t0441OutBlock1", "medosu", i).strip(), # 구분
                int(self.GetFieldData("t0441OutBlock1", "jqty", i)), #잔고수량
                int(self.GetFieldData("t0441OutBlock1", "cqty", i)), #청산가능수량
                self.GetFieldData("t0441OutBlock1", "medocd", i).strip(), #매매구분
                int(self.GetFieldData("t0441OutBlock1", "dtsunik", i)), #매매손익
                int(self.GetFieldData("t0441OutBlock1", "mamt", i)), #총매입금액
                float(self.GetFieldData("t0441OutBlock1", "sunikrt", i)), #수익율
                float(self.GetFieldData("t0441OutBlock1", "price", i)), #현재가
                float(self.GetFieldData("t0441OutBlock1", "pamt", i)) #평균단가
                ]
                self.total_data.append(tmp_data)
        else:
            tmp_data = ["", "", 0, 0, "", 0, 0, 0.0, 0.0, 0.0]
            self.total_data.append(tmp_data)
        df_total_data = pd.DataFrame(self.total_data)
        df_total_data.columns = [
            "종목코드",
            "구분",
            "잔고수량",
            "청산가능수량",
            "매매구분",
            "매매손익",
            "총매입금액",
            "수익율",
            "현재가",
            "평균단가"
        ]
        
        self.df_received_data = pd.concat([df_total, df_total_data], axis=1)
            
    def start(self):
        TROccurs.start(self, self.code)

    def singleRequest(self):
        self.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\t0441.res" # RES 파일 등록
        self.SetFieldData("t0441InBlock", "accno", 0, INI.AccNr) 
        self.SetFieldData("t0441InBlock", "passwd", 0, INI.TradePW)
        self.SetFieldData("t0441InBlock", "cts_expcode", 0, '' )
        self.SetFieldData("t0441InBlock", "cts_medocd", 0, '' )

        err_code = self.Request(False) # data 요청하기 -- 연속조회인경우만 True

        if err_code < 0:
            print("error... {0}".format(err_code))    
            
class OptionNormalOrder(TROccurs):
    def __init__(self):
        TROccurs.__init__(self) # 추상화 클래스 생성자
        
        self.code = "CFOAT00100" #TR Code
        self.is_data_received = False
    
    def OnReceiveData(self, tr_code):
        self.is_data_received = True
        self.total_data = []
        tmp_data = [
        self.GetFieldData("CFOAT00100OutBlock1", "FnoOrdPrc", 0),
        self.GetFieldData("CFOAT00100OutBlock1", "OrdQty", 0),
        self.GetFieldData("CFOAT00100OutBlock2", "OrdNo", 0),
        self.GetFieldData("CFOAT00100OutBlock1", "AcntNo", 0),
        self.GetFieldData("CFOAT00100OutBlock1", "Pwd", 0),
        self.GetFieldData("CFOAT00100OutBlock1", "BnsTpCode", 0)
        ]

        print("주문후 결과값: ", tmp_data)
        
        self.total_data.append(tmp_data)
        df_total_data = pd.DataFrame(self.total_data)
        df_total_data.columns = [
            "주문가격",
            "주문수량",
            "주문번호",
            "계좌번호",
            "비밀번호",
            "매도수구분"
        ]
        
        self.df_received_data = df_total_data
        
    def start(self):
        TROccurs.start(self, self.code)
        
    def singleRequest(self, code, bsgubun, currP, OrdQty, AccNr, TradePW):
        self.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\CFOAT00100.res" # RES 파일 등록
        self.SetFieldData("CFOAT00100InBlock1", "AcntNo", 0, AccNr) # 계좌번호
        self.SetFieldData("CFOAT00100InBlock1", "Pwd", 0, TradePW) # 계좌비밀번호
        self.SetFieldData("CFOAT00100InBlock1", "FnoIsuNo", 0, code) # 종목코드
        self.SetFieldData("CFOAT00100InBlock1", "BnsTpCode", 0, bsgubun) # 매매구분
        # 매매구분 
        # 1 - 매도 
        # 2 - 매수
        self.SetFieldData("CFOAT00100InBlock1", "FnoOrdprcPtnCode", 0, "00") 
        # 호가코드 
        # 00 - 지정가 
        # 03 - 시장가 
        # 05 - 조건부지정가
        # 06 - 최유리지정가 
        # 10 - 지정가(IOC) 
        # 20 - 지정가(FOK)
        # 13 - 시장가(IOC)
        # 23 - 시장가(FOK)
        # 16 - 최유리지정가(IOC)
        self.SetFieldData("CFOAT00100InBlock1", "FnoOrdPrc", 0, currP) # 주문가격 # 매수일 경우 + 1, 매도인 경우 -1로 지정가 주문 
        self.SetFieldData("CFOAT00100InBlock1", "OrdQty", 0, OrdQty) # 주문수량
        
        err_code = self.Request(False) # data 요청하기 -- 연속조회인경우만 True

        if err_code < 0:
            print("error... {0}".format(err_code))  
            
def getKoreanOptionInfo(int_mon, Option):
    info_handler = KoreanOptionInfo.getInstance()
    info_handler.start()
    info_handler.singleRequest(int_mon, Option)

    while info_handler.is_data_received == False:
        pythoncom.PumpWaitingMessages()

    df_data =  info_handler.getResultData()

    module_logger.info("옵션 코드정보 가져오기 성공")

    return df_data

def getOption8414(option_code, tick_unit):
    info_handler = Option8414.getInstance()
    info_handler.start()
    info_handler.singleRequest(str(option_code), tick_unit)

    while info_handler.is_data_received == False:
        pythoncom.PumpWaitingMessages()

    df_data =  info_handler.getResultData()

    module_logger.info("옵션 틱봉시가 가져오기 성공")

    return df_data

def getOption8415(option_code, min_unit):
    info_handler = Option8415.getInstance()
    info_handler.start()
    info_handler.singleRequest(option_code, min_unit)

    while info_handler.is_data_received == False:
        pythoncom.PumpWaitingMessages()

    df_data =  info_handler.getResultData()

    module_logger.info("옵션 분봉시가 가져오기 성공")

    return df_data

def getOption0441():
    info_handler = Option0441.getInstance()
    info_handler.start()
    info_handler.singleRequest()

    while info_handler.is_data_received == False:
        pythoncom.PumpWaitingMessages()

    df_data =  info_handler.getResultData()

    module_logger.info("옵션 잔고/잔량 가져오기 성공")

    return df_data

def SendOrderFo(order): # singleRequest(self, code, bsgubun, currP, OrdQty, orderType = "03"):
    name, code, bsgubun, currP, OrdQty, AccNr, TradePW = order
    info_handler = OptionNormalOrder.getInstance()
    info_handler.start()
    info_handler.singleRequest(code, bsgubun, currP, OrdQty, AccNr, TradePW)

    while info_handler.is_data_received == False:
        pythoncom.PumpWaitingMessages()

    df_data =  info_handler.getResultData()

    module_logger.info(f"옵션 주문: {name}", )

    return df_data

               
               

