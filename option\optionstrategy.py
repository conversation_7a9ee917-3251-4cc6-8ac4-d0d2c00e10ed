from utils.config import columns_jg, ui_num
from utils.util import int_hms, now
from PyQt5.QtTest import *
from utils.maketimebar import getHMTFromTime, getChartTime
from utils.configparserINI import ConfigparserINI
import pandas as pd
import numpy as np
import math
import talib


class OptionStrategy:
    def __init__(self, qlist, option_type="call"):
        self.option_type = option_type.lower()  # "call" 또는 "put"
        self.option_name = "콜" if self.option_type == "call" else "풋"
        
        self.windowQ            = qlist[0]
        self.receiverQ          = qlist[1]
        self.traderQ            = qlist[2]
        self.CallStrQ           = qlist[3]
        self.PutStrQ            = qlist[4]
        self.teleQ              = qlist[5]
        
        # 옵션 타입에 따른 큐 설정
        self.strategyQ = self.CallStrQ if self.option_type == "call" else self.PutStrQ
        
        self.df_jg = pd.DataFrame(columns=columns_jg)
        
        self.INI = ConfigparserINI()
        self.strategy_begintime = int(self.INI.strategy_begintime)
        self.strategy_endtime   = int(self.INI.strategy_endtime)
        self.sellTime           = int(self.INI.selltime)
        
        self.InvestMoney = int(self.INI.jangChoInvest) * 1000
         
        self.Stoploss        = abs(float(self.INI.stoploss))
        self.absProfittarget = abs(float(self.INI.absProfitTarget))
        
        self.minStrategy     = int(self.INI.minStrategy)
        self.realMinStrategy = int(self.INI.realMinStrategy)
        self.Bong            = int(self.INI.Bong)
        self.RequestMin = int(self.INI.ResetTr)  
        
        self.stoch_period1 = int(self.INI.stoch_period1)
        self.stoch_period2 = int(self.INI.stoch_period2)
        self.stoch_period3 = int(self.INI.stoch_period3)
        self.cci_period    = int(self.INI.cci_period)
        self.scci_period   = int(self.INI.scci_period)
        self.dmi_period    = int(self.INI.dmi_period)
        self.adx_period    = int(self.INI.adx_period)
        
        self.cci_level    = int(self.INI.cci_level)
        self.scci_level    = int(self.INI.scci_level)
        self.adx_level    = int(self.INI.adx_level)
        self.stoch_level  = int(self.INI.stoch_level)
        
        self.sell_adx_level = int(self.INI.sell_adx_level)
        
        self.maxlen = max(self.stoch_period1 + self.stoch_period2 + self.stoch_period3 - 2, self.cci_period, self.dmi_period, self.adx_period)
        
        self.buyCondition = False
        self.sellCondUse = int(self.INI.sellCondUse)
        self.sellCondition = False
        self.codeRequest = False
        self.num = 0
        self.ADXagerage = 0.0
        
        self.strategyStart   = "Start"
        self.code = None
        self.Totalticks = {}     
        self.dict_hilo  = {}
        self.buyStatus = False
        self.tickC = 0  
          
        self.dict_min_ar   = {}
        self.dict_min_ar2  = {}

        self.포지션, self.수익금, self.수익률, self.매입가, self.보유수량 = None, 0, 0.0, 0, 0
        self.익절매도, self.손절매도, self.매수청산 = False, False, False
        
        self.Start()
        
    def Start(self):
        self.windowQ.put([ui_num['로그텍스트'], f'시스템 명령 실행 - {self.option_name} 전략 연산 시작'])
        self.teleQ.put(f'{self.option_name} 전략 연산 시작')
        
        while True:
            data = self.strategyQ.get()
            if type(data) == str:
                if data == '전략프로세스종료':
                    break
            elif type(data) == pd.DataFrame:
                self.df_jg = data
                self.PutGsjmAndDeleteHilo()
            elif type(data) == list:
                if len(data) == 2:
                    self.UpdateList(data)
                elif len(data) == 3:
                    try:
                        if data[0] == f"{self.option_name}분봉받기": # and data[1] in self.option_codes:
                            self.code = data[1]
                            if int_hms() > 84500 and len(data[2]) >= 1:
                                data[2] = data[2][:-1]
                                self.dict_min_ar[self.code] = data[2]
                                self.num = len(self.dict_min_ar[self.code]) + 1
                            self.requestOpen = self.dict_min_ar[self.code][0, 1]
                    except Exception as e:
                        self.windowQ.put([ui_num['로그텍스트'], f"전략연산준비"])
                elif len(data) == 4:
                    self.StrategyConstruction(data)

        self.windowQ.put([ui_num['로그텍스트'], f'시스템 명령 실행 - {self.option_name} 전략 연산 종료'])
        
    def UpdateList(self, data):
        gubun, code = data
        if gubun == f"거래할{self.option_name}옵션":
            self.option_codes = code
        elif gubun == '수동청산':
            self.buyStatus = False
        elif gubun == '전략중지':
            self.strategyStart = '전략중지'
            
    def StrategyConstruction(self, data):
        체결시간, 현재가, 틱당거래량, 종목코드 = data
        
        분봉시가, 분봉고가, 분봉저가, 분봉종가, 분당거래량 = 현재가, 현재가, 현재가, 현재가, 틱당거래량 

        ########################### 지표값 초기화 ##################################
        MinCCI, sMinCCI, MinDMIPLUS, MinDMIMINUS, MinADX = 0.0, 0.0, 0.0, 0.0, 0.0
        MinSTOCK, MinSTOCD = 0.0, 0.0
        FilterCond1, FilterCond2 = False, False
        ##########################################################################

        시분초 = int(체결시간)
        gtime = abs(시분초)
        intervaltime = getHMTFromTime(gtime, self.Bong*60)
        lChartTimebefore, _ = getChartTime(intervaltime, self.Bong*60)
        초분봉시간 = int(float(lChartTimebefore)) 
        
        if 종목코드 not in self.dict_hilo.keys():
            self.dict_hilo[종목코드] = 0.0  
        if 종목코드 not in self.Totalticks.keys():
            self.Totalticks[종목코드] = 0
       
        if 체결시간:
            self.Totalticks[종목코드] += 1
            new_data_P = [초분봉시간, 현재가, 현재가, 현재가, 현재가, 틱당거래량]
            if 종목코드 not in self.dict_min_ar.keys():
                self.dict_min_ar[종목코드] = np.array([new_data_P]) 
            if 종목코드 not in self.dict_min_ar2.keys():
                self.dict_min_ar2[종목코드] = np.array([new_data_P])                                                         
                                         
            if 초분봉시간 == self.dict_min_ar2[종목코드][-1, 0]:
                if self.tickC == 0: 
                    self.tickC += 1   
                    분봉시가, 분봉고가, 분봉저가, 분당거래량 = 현재가, 현재가, 현재가, 틱당거래량                 
                elif self.tickC >= 1:
                    _, 분봉고가, 분봉저가, _, 분당거래량 = self.dict_min_ar2[종목코드][-1, 1:6]
                    self.tickC += 1 

                분봉고가 = 현재가 if 현재가 > 분봉고가 else 분봉고가
                분봉저가 = 현재가 if 현재가 < 분봉저가 else 분봉저가
                분당거래량 += 틱당거래량     
                
                if self.realMinStrategy:                                                   
                    if len(self.dict_min_ar[종목코드]) > self.maxlen: 
                        ############################# 봉미완성 Stoch, CCI, DMI, ADX ################################# 
                        # 통합 모듈 사용
                        high = np.r_[self.dict_min_ar[종목코드][:, 2], np.array([분봉고가])]
                        low = np.r_[self.dict_min_ar[종목코드][:, 3], np.array([분봉저가])]
                        close = np.r_[self.dict_min_ar[종목코드][:, 4], np.array([현재가])]
                        
                        # 지표 계산 함수 호출
                        MinCCI, sMinCCI, MinSTOCK, MinSTOCD, MinDMIPLUS, MinDMIMINUS, MinADX = self.calculate_indicators(high, low, close)
                        
                        # 매수/매도 조건 설정
                        self.set_conditions(MinCCI, sMinCCI, MinSTOCK, MinSTOCD, MinDMIPLUS, MinDMIMINUS, MinADX)
                        ################################# 지표계산 끝 ##############################################

            else:  
                if self.tickC > 0:                        
                    self.tickC = 0       
                    분봉시가, 분봉고가, 분봉저가, 분봉종가, 분당거래량 = self.dict_min_ar2[종목코드][-1, 1:6]    
                    
                    if self.minStrategy:           
                        if len(self.dict_min_ar[종목코드]) > self.maxlen: 
                            ############################# 봉완성 Stoch, CCI, DMI, ADX ################################# 
                            # 통합 모듈 사용
                            high = np.r_[self.dict_min_ar[종목코드][:, 2], np.array([분봉고가])]
                            low = np.r_[self.dict_min_ar[종목코드][:, 3], np.array([분봉저가])]
                            close = np.r_[self.dict_min_ar[종목코드][:, 4], np.array([분봉종가])]
                            
                            # 지표 계산 함수 호출
                            MinCCI, sMinCCI, MinSTOCK, MinSTOCD, MinDMIPLUS, MinDMIMINUS, MinADX = self.calculate_indicators(high, low, close)
                            
                            # 매수/매도 조건 설정
                            indicator_values = self.set_conditions(MinCCI, sMinCCI, MinSTOCK, MinSTOCD, MinDMIPLUS, MinDMIMINUS, MinADX)
                            ################################# 지표계산 끝 ##############################################
                            
                            # self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}지표: {indicator_values}'])

            if 종목코드 and self.Totalticks[종목코드] >= 1:   
                if 종목코드 in self.df_jg.index:
                    self.포지션 = self.df_jg["구분"][종목코드]
                    self.매입가 = float(self.df_jg["매입가"][종목코드])
                    self.보유수량 = int(self.df_jg["보유수량"][종목코드])
                    self.매입금액 = int(self.df_jg["매입금액"][종목코드]) 
                    self.수익률 = float(self.df_jg["수익율"][종목코드]) 

                    if self.수익률 != 0.0 and 종목코드 not in self.dict_hilo.keys():
                        self.dict_hilo[종목코드] = self.수익률
                    else:
                        if self.수익률 > self.dict_hilo[종목코드]:
                            self.dict_hilo[종목코드] = self.수익률    
                 
                #################################################   전략 부분 시작    #########################################################                         
                if self.strategyStart != '전략중지':                             
                    if 종목코드 not in self.df_jg.index:
                        매수 = False
                        매수수량 = 0
  
                        if self.strategy_begintime <= 시분초 < self.strategy_endtime:
                            if self.buyCondition and not self.buyStatus: 
                                print(f"{self.option_name}매수")
                                매수수량 = math.floor(self.InvestMoney/(현재가*250_000)) 
                                매수 = True
                                self.buyStatus = True                            
                        if 매수:       
                            self.손절매도 = False
                            self.익절매도 = False  
                            self.매수청산 = False 
                            self.sellCondition = False
                            예상체결가 = round(현재가 + 0.1, 2)                      
                            self.windowQ.put([ui_num['단순텍스트'], f'종목코드: {종목코드}, 매수, 매입가: {현재가}, 진입수량: {매수수량}']) 
                            self.traderQ.put([f"{self.option_name}매수", 종목코드, '2', 예상체결가, 매수수량, now()])                          
                    
                    else:
                        매수청산 = False  
                        매도수량 = 0
                        
                        if 시분초 < self.sellTime:
                            if self.sellCondition and not self.매수청산:
                                self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}매수청산: 지표청산']) 
                                매도수량 = self.보유수량
                                매수청산 = True
                                self.매수청산 = True
                                self.buyStatus = False
                                self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}매수청산: 지표청산, 매도수량: {매도수량}']) 

                            elif self.수익률 < -self.Stoploss and not self.손절매도:
                                self.windowQ.put([ui_num['단순텍스트'], f'손절율: {self.수익률} {self.Stoploss}']) 
                                매수청산 = True
                                매도수량 =  self.보유수량
                                self.손절매도 = True
                                self.buyStatus = False
                                self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}매수청산: 수익률 < self.Stoploss, {self.수익률}'])
                                
                            elif self.수익률 > self.absProfittarget and not self.익절매도:
                                self.windowQ.put([ui_num['단순텍스트'], f'익절율: {self.수익률} {self.absProfittarget}']) 
                                매수청산 = True
                                매도수량 =  self.보유수량
                                self.익절매도 = True
                                self.buyStatus = False
                                self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}매수청산: 수익률 > self.absProfittarget , {self.수익률}'])
                            
                        if 매수청산:
                            self.buyCondition = False
                            예상체결가 = round(현재가 - 0.1, 2)
                            self.windowQ.put([ui_num['단순텍스트'], f'종목코드: {종목코드}, 매수청산, 청산가: {현재가}, 청산수량: {매도수량}']) 
                            self.traderQ.put([f"{self.option_name}매수청산", 종목코드, '1', 예상체결가, int(매도수량), now()]) 
                            self.receiverQ.put(["종목코드불러오기", int_hms()]) 
                
                ############################################################################################################################## 

            if 종목코드 and self.Totalticks[종목코드] > 1: 
                if 초분봉시간 == self.dict_min_ar[종목코드][-1, 0]:
                    self.dict_min_ar2[종목코드][-1, 0:6] = 초분봉시간, 분봉시가, 분봉고가, 분봉저가, 현재가, 분당거래량                        
                else: 
                    if self.option_type == "call" and len(self.dict_min_ar[종목코드]) % self.RequestMin == 0: 
                        self.receiverQ.put(["콜종목코드불러오기", int_hms()])
                        
                    new_data = [초분봉시간, 분봉시가, 분봉고가, 분봉저가, 현재가, 분당거래량]
                    self.dict_min_ar[종목코드] = np.r_[self.dict_min_ar[종목코드], np.array([new_data])]
                    if len(self.dict_min_ar[종목코드]) > 400:
                        self.dict_min_ar[종목코드] = np.delete(self.dict_min_ar[종목코드], 0, axis=0)                                              
                    # 시간 = self.dict_min_ar[종목코드][-1, 0]
                    # open = self.dict_min_ar[종목코드][-1, 1]
                    # high = self.dict_min_ar[종목코드][-1, 2]
                    # low = self.dict_min_ar[종목코드][-1, 3]
                    # close = self.dict_min_ar[종목코드][-1, 4]
                    # self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}옵션 1분봉: {len(self.dict_min_ar[종목코드])}, {시간}, {open}, {high}, {low}, {close}'])

                    if self.option_type == "call":
                        self.receiverQ.put(["분봉불러오기", 종목코드])
                
    def PutGsjmAndDeleteHilo(self):
        if len(self.dict_hilo) > 0:
            for code in list(self.dict_hilo.keys()):
                if code not in self.df_jg.index:
                    del self.dict_hilo[code]
        
    def diy_ema_dmi_adx(self, high, low, close, period):
        prev_close = pd.Series(close).shift(1)
        tr1 = pd.Series(high) - pd.Series(low)
        tr2 = (pd.Series(high) - prev_close).abs()
        tr3 = (pd.Series(low) - prev_close).abs()
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        up_move = pd.Series(high) - pd.Series(high).shift(1)
        down_move = pd.Series(low).shift(1) - pd.Series(low)
        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)

        alpha = 2 / (period + 1)
        smoothed_tr = pd.Series(tr).ewm(alpha=alpha, adjust=False).mean()
        smoothed_plus_dm = pd.Series(plus_dm).ewm(alpha=alpha, adjust=False).mean()
        smoothed_minus_dm = pd.Series(minus_dm).ewm(alpha=alpha, adjust=False).mean()

        ema_di_plus = (smoothed_plus_dm / smoothed_tr) * 100
        ema_di_minus = (smoothed_minus_dm / smoothed_tr) * 100
        ema_dx = (abs(ema_di_plus - ema_di_minus) / (ema_di_plus + ema_di_minus + 1e-10)) * 100
        ema_adx = pd.Series(ema_dx).ewm(alpha=alpha, adjust=False).mean()

        return ema_di_plus.values, ema_di_minus.values, ema_adx.values 

    # 지표 계산 함수를 별도로 분리
    def calculate_indicators(self, high, low, close):
        # CCI 계산 (TA-Lib)
        MinCCI = talib.CCI(high, low, close, timeperiod=self.cci_period)
        sMinCCI = talib.CCI(high, low, close, timeperiod=self.scci_period)
        
        # Stochastic 계산
        MinSTOCK, MinSTOCD = talib.STOCH(high, low, close, 
                                         fastk_period=self.stoch_period1, 
                                         slowk_period=self.stoch_period2,
                                         slowk_matype=0, 
                                         slowd_period=self.stoch_period3, 
                                         slowd_matype=1)
        
        # DMI, ADX 계산
        MinDMIPLUS, MinDMIMINUS, MinADX = self.diy_ema_dmi_adx(high, low, close, self.adx_period)
        
                                   
        ############################# 봉완성 MA, MACD, BB, ATR, RSI #################################           
        # M_MA20 = talib.MA(close, timeperiod=20)
        # M_BBU, M_BBM, M_BBL = talib.BBANDS(close, timeperiod=5, nbdevup=2, nbdevdn=2, matype=0)
        # M_RSI = talib.RSI(close, timeperiod=14)
        # M_MACD, M_MACDS, M_MACDH = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
        # M_ATR = talib.ATR(high, low, close, timeperiod=14)
      ################################# 지표계산 끝 ##############################################

        
        return MinCCI, sMinCCI, MinSTOCK, MinSTOCD, MinDMIPLUS, MinDMIMINUS, MinADX

    # 매수/매도 조건 설정 함수 분리
    def set_conditions(self, MinCCI, sMinCCI, MinSTOCK, MinSTOCD, MinDMIPLUS, MinDMIMINUS, MinADX):
        self.ADXagerage = (MinADX[-6] +  MinADX[-5] + MinADX[-4] + MinADX[-3] + MinADX[-2]) / 5 
        
        self.buyCondition = (MinDMIPLUS[-1] > MinDMIMINUS[-1] and 
                             MinCCI[-1] > self.cci_level and 
                             MinADX[-1] > self.adx_level and 
                             MinSTOCK[-1] > MinSTOCD[-1] and 
                             MinSTOCD[-1] > self.stoch_level)
        
        FilterCond1 = sMinCCI[-1] < self.scci_level
        FilterCond3 = MinDMIPLUS[-1] < MinDMIMINUS[-1]
        FilterCond2 = MinADX[-1] < self.sell_adx_level
               
        if self.sellCondUse:
            self.sellCondition = FilterCond1 and FilterCond2 and FilterCond3
        else:
            self.sellCondition = FilterCond1
        
        # self.windowQ.put([ui_num['단순텍스트'], f'{self.option_name}옵션 1분봉: buyCondition {self.buyCondition}, sellCondition {self.sellCondition}'])
        
        return MinCCI[-1], sMinCCI[-1], MinDMIPLUS[-1], MinDMIMINUS[-1], MinADX[-1], MinSTOCK[-1], MinSTOCD[-1] 