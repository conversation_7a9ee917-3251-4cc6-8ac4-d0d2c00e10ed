import configparser

class ConfigparserINI:
    def __init__(self):
        self.config = configparser.ConfigParser()
        self.config.read('./setting_config.ini', encoding='utf-8')
        
        ServerSelection = self.config["ServerSelection"]
        self.real_market = ServerSelection["real_market"]
        self.demo_market = ServerSelection["demo_market"]
        self.telegram = ServerSelection['telegram']
        self.ID = ServerSelection["id"]
        self.PW = ServerSelection["pw"]
        self.CERTPW = ServerSelection["certpw"]
        self.AccNr = ServerSelection["accnr"]
        self.TradePW = ServerSelection["tradepw"]
        self.teleID     = ServerSelection["teleID"]
        self.teleToken  = ServerSelection["teleToken"]
        self.realMinStrategy = ServerSelection["realminstrategy"]
        self.minStrategy  = ServerSelection["minstrategy"]
        self.THU = ServerSelection['thu']
        self.MON = ServerSelection['mon']
        self.MoOption = ServerSelection['mooption']
          
        StragTime = self.config["StragTime"]  
        self.strategy_begintime = StragTime['strategy_begintime']
        self.strategy_endtime = StragTime['strategy_endtime']
        self.selltime = StragTime['selltime']  
        self.receiverTime = StragTime['receivertime']    
        self.traderTime = StragTime['tradertime']  
        self.exitTime = StragTime['exittime']  
        
        StragVar = self.config["StragVar"] 
        self.jangChoInvest = StragVar['jangchoinvest']
        self.priceMin = StragVar['pricemin']
        self.priceMax = StragVar['pricemax']   
        self.Bong = StragVar['bong']  
        self.ResetTr = StragVar['resettr']  

        StragVar2 = self.config["StragVar2"] 
        self.stoch_period1 = StragVar2['stoch_period1']
        self.stoch_period2 = StragVar2['stoch_period2']
        self.stoch_period3 = StragVar2['stoch_period3']
        self.stoch_level = StragVar2['stoch_level']
        self.cci_period = StragVar2['cci_period']
        self.cci_level = StragVar2['cci_level']
        self.scci_period = StragVar2['scci_period']
        self.scci_level = StragVar2['scci_level']
        self.dmi_period = StragVar2['dmi_period']
        self.adx_period = StragVar2['adx_period']
        self.adx_level = StragVar2['adx_level']
        self.sellCondUse = StragVar2['sellconduse']
        self.sell_adx_level = StragVar2['sell_adx_level']
        
        StragPT = self.config["StragPTSL"]
        self.stoploss = StragPT['stopLoss']
        self.absProfitTarget = StragPT['absprofittarget']      
