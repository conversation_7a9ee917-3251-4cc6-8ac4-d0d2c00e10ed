
@echo off
title "%~dp0"
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"

if '%errorlevel%' NEQ '0' (
    echo Requesting administrative privileges...
    goto UACPrompt
) else ( goto gotAdmin )

:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    set params = %*:"=""
    echo UAC.ShellExecute "cmd.exe", "/c %~s0 %params%", "", "runas", 1 >> "%temp%\getadmin.vbs"

    "%temp%\getadmin.vbs"
    del "%temp%\getadmin.vbs"
    exit /B

:gotAdmin
    pushd "%CD%"
    CD /D "%~dp0"
    python -m pip install --upgrade pip
    python -m pip install ntplib numpy==1.24.2
    python -m pip install pandas==2.0.3 websockets==10.4 Pillow==9.4.0 contourpy==1.0.7 cryptography==36.0.0 python-telegram-bot==13.15
    python -m pip install psutil pyqt5 BeautifulSoup4 lxml pyttsx3 squarify PyQtWebEngine
    python -m pip install pyqtgraph matplotlib==3.7.1 python-dateutil
    python -m pip install TA_Lib-0.4.27-cp311-cp311-win32.whl
    pause