import sys
import pandas as pd
from utils.config import ui_num, columns_tg, columns_jg, columns_td, columns_tt, columns_cj
from option.xingAPI import *
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication
from utils.util import GetOptionLongPgSgSp, now, strf_time, timedelta_sec, int_hms, qtest_qwait
from utils.configparserINI import ConfigparserINI

class Updater(QThread):
    signal = pyqtSignal(list)

    def __init__(self, traderQ):
        super().__init__()
        self.traderQ = traderQ

    def run(self):
        while True:
            data = self.traderQ.get()
            if type(data) == list:
                self.signal.emit(data)

class Trader:
    def __init__(self, qlist):
        app_ = QApplication(sys.argv)

        self.windowQ            = qlist[0]
        self.receiverQ          = qlist[1]
        self.traderQ            = qlist[2]
        self.CallStrQ           = qlist[3]
        self.PutStrQ            = qlist[4]
        self.teleQ              = qlist[5]

        self.INI = ConfigparserINI()
        self.real_market = int(self.INI.real_market)
        if self.real_market: self.Server = 1
        self.demo_market = int(self.INI.demo_market)
        if self.demo_market: self.Server = 0
        self.sellTime  = int(self.INI.selltime)
        self.AccNr = self.INI.AccNr
        self.TradePW = self.INI.TradePW

        self.df_cj = pd.DataFrame(columns=columns_cj)
        self.df_jg = pd.DataFrame(columns=columns_jg)
        self.df_tg = pd.DataFrame(columns=columns_tg)
        self.df_td = pd.DataFrame(columns=columns_td)
        self.df_tt = pd.DataFrame(columns=columns_tt)
        
        self.jeobsu_dic = {}        
        self.dict_intg = {
            '장운영상태': 1,
            '당일실현손익': 0,
        }
        self.dict_strg = {
            '당일날짜': strf_time('%Y%m%d'),
        }
        self.dict_bool = {
            '트레이더시작': False,
            '프로세스종료': False,
            '실현수익조회': False,   
            '전략잔고청산': False,
            '시간청산' : False,         
        }
        curr_time      = now()
        self.dict_time = {
            '주문시간': curr_time,
            '시스템확인전송' : curr_time,
        }
        self.xas = XASession()
        self.order = OptionNormalOrder()

        self.xar_O0 = XAReal(self)
        self.xar_C0 = XAReal(self)
        self.xar_O0.RegisterRes('O01')
        self.xar_C0.RegisterRes('C01')
 
        self.call_option  = ''
        self.put_option   = ''

        self.XingLogin()       
        
        self.updater = Updater(self.traderQ)
        self.updater.signal.connect(self.BuySellUpdateJangolist)
        self.updater.start()

        self.qtimer1 = QTimer()
        self.qtimer1.setInterval(1100)
        self.qtimer1.timeout.connect(self.Scheduler)
        self.qtimer1.start()
        
        self.qtimer2 = QTimer()
        self.qtimer2.setInterval(250)
        self.qtimer2.timeout.connect(self.PutJangoDF)
        self.qtimer2.start()

        self.traderStop = False

        app_.exec_()
    
    def XingLogin(self):
        self.xas.Login(self.Server,self.INI.ID, self.INI.PW, self.INI.CERTPW) 
        account_list = []
        acc_Name_list = {}
        count = self.xas.GetAccountListCount()
        for i in range(count):
            account = self.xas.GetAccountList(i)
            account_list.append((account))
            acc_Name_list.update({account: self.xas.GetAcctDetailName(account)})
        print("전체 계좌 리스트: ", acc_Name_list)
          
        print("XingAPI 트레이더 로그인")
        if int(strf_time('%H%M%S')) >= 90000:
            self.dict_intg['장운영상태'] = 21
        self.receiverQ.put(['트레이더시작', True])    
        self.windowQ.put([ui_num['로그텍스트'], '시스템 명령 실행 - XingAPI 트레이더 로그인'])
        
    def BuySellUpdateJangolist(self, data):
        if not self.traderStop:
            if len(data) == 6:
                self.BuySell(data[0], data[1], data[2], data[3], data[4], data[5]) 
            elif len(data) == 1:
                if data[0] == '잔고청산' and not self.dict_bool['전략잔고청산']:
                    self.JangoCheongsan()
                    self.dict_bool['전략잔고청산'] = True
            elif len(data) == 3:
                _, code, c = data
                try:
                    if c != self.df_jg['현재가'][code]:
                        jg = self.df_jg['매입금액'][code]
                        jc = int(self.df_jg['보유수량'][code])
                        pg, sg, sp = GetOptionLongPgSgSp(jg, jc *c *250_000)
                        columns = ['현재가', '수익율', '평가손익', '평가금액']
                        self.df_jg.loc[code, columns] = c, sp, sg, pg
                except:
                    pass
            elif len(data) == 2:
                if data[0] == '거래할콜옵션':
                    self.call_option = data[1] 
                elif data[0] == '거래할풋옵션':
                    self.put_option = data[1] 
        
    def BuySell(self, ordertypename, code, gubun, orderprice, qty, signal_time):
        gap = (now() - signal_time).total_seconds()
        # self.windowQ.put([ui_num['단순텍스트'], f'시그널 주문 시간 - [{ordertypename}] 발생시간과 주문시간의 차이는 [{gap:.6f}]초입니다.'])
        self.SendOrder([ordertypename, code, gubun, round(orderprice, 2), qty, self.AccNr, self.TradePW])
        
    def SendOrder(self, order):        
        if now() < self.dict_time['주문시간']:
            next_time = (self.dict_time['주문시간'] - now()).total_seconds()
            QTimer.singleShot(int(next_time * 1000), lambda: self.SendOrder(order))
            return
        
        ret = SendOrderFo(order) 
       
        if ret.loc[:, '주문수량'][0] != "":
            self.windowQ.put([ui_num['로그텍스트'], f'{order[1]} {order[4]}계약 {order[0]} 주문완료'])
        else:
            self.windowQ.put([ui_num['로그텍스트'], f'{order[1]} {order[4]}계약 {order[0]} 주문 실패'])
        self.dict_time['주문시간'] = timedelta_sec(0.5)
            
        ########## 확인요 #####################
        if order[0] == '수동청산':
            if order[1] in self.df_jg.index and ('209' in order[1] or '2AF' in order[1] or '201' in order[1]):
                self.CallStrQ.put(['수동청산', order[1]])  
            elif order[1] in self.df_jg.index and ('309' in order[1] or '3AF' in order[1] or '301' in order[1]):
                self.PutStrQ.put(['수동청산', order[1]])  
            
            df_jg = self.df_jg.drop(labels=order[1])
            self.windowQ.put([ui_num['잔고목록'], df_jg]) 
            
    def Scheduler(self): 
        if not self.dict_bool['트레이더시작']:
            self.OperationRealreg()

        if 84503 <= int_hms():
            cur_hold = self.get_0441()   
            if cur_hold != "":     
                try:
                    self.dict_intg['당일실현손익'] =  cur_hold[0]['tr_profit']
                except TypeError:
                    pass                
                
                self.windowQ.put([ui_num['실현손익'], self.df_tg]) 
                    
                self.result = []
                for each in cur_hold[1] :
                    self.result.append(each['code'])

                if self.result != []:
                    self.df_jg = pd.DataFrame(columns=columns_jg)
                    for each in cur_hold[1]:
                        profit = each['appamt'] - each['mamt']
                        if each['mamt'] != 0:
                            profitrate = float(profit) / each['mamt'] *100
                        index_value = each['code']
                        self.df_jg.loc[index_value, '종목코드'] = str(each['code'])
                        self.df_jg.loc[index_value, '보유수량'] = int(each['qty'])
                        self.df_jg.loc[index_value, '매입가'] = float(each['unitorderprice'])
                        self.df_jg.loc[index_value, '현재가'] = float(each['price'])
                        self.df_jg.loc[index_value, '수익율'] = float(profitrate)
                        self.df_jg.loc[index_value, '평가손익'] = int(profit)
                        self.df_jg.loc[index_value, '매입금액'] = int(each['mamt'])
                        self.df_jg.loc[index_value, '평가금액'] = int(each['appamt'])
                        self.df_jg.loc[index_value, '구분'] = str(each['buy_sell'])

                    self.windowQ.put([ui_num['잔고목록'], self.df_jg])  
                elif self.result == []:  
                    try:
                        ########## 확인요 #####################
                        self.df_jg = pd.DataFrame(columns=columns_jg)
                        self.windowQ.put([ui_num['잔고목록'], self.df_jg])                         
                    except:
                        return

        if int_hms() >= self.sellTime and not self.dict_bool['시간청산']:
            self.dict_bool['시간청산'] = True
            self.JangoCheongsan()

        if 154600 <= int(strf_time('%H%M%S')): 
            self.SysExit()  
        
        self.UpdateTotaljango()

        if now() > self.dict_time['시스템확인전송']:
            self.teleQ.put(f'{int_hms()} 시스템 작동중입니다.')
            self.dict_time['시스템확인전송'] = timedelta_sec(60*2)
    
    def JangoCheongsan(self):        
        for code in self.df_jg.index:                
            c = self.df_jg['현재가'][code]
            oc = self.df_jg['보유수량'][code]
            print("매수인가?: ", self.df_jg['구분'][code] == '매수')
            if self.df_jg['구분'][code] == '매수':
                on = "1"               
                if ('309' in code or '3AF' in code or '301' in code):
                    c = round(c - 0.1, 2)
                    self.traderQ.put([f'{code}일괄청산', code, on, c, oc, now()])    
                    self.windowQ.put([ui_num['단순텍스트'], f'{code}일괄풋청산 전략종료'])
                    self.PutStrQ.put(['전략중지', True])  
                    self.CallStrQ.put(['전략중지', True]) 
                    print("전략 콜 잔고청산")
                    qtest_qwait(2)
                elif ('209' in code or '2AF' in code or '201' in code):
                    c = round(c - 0.1, 2)
                    self.traderQ.put([f'{code}일괄청산', code, on, c, oc, now()])  
                    self.windowQ.put([ui_num['단순텍스트'], f'{code}일괄콜청산 전략종료'])
                    self.CallStrQ.put(['전략중지', True]) 
                    self.PutStrQ.put(['전략중지', True]) 
                    print("전략 풋 잔고청산")
                    qtest_qwait(2)
                    
        self.windowQ.put([ui_num['로그텍스트'], '시스템 명령 실행 - 전략 잔고청산 주문 완료'])
    
    def OperationRealreg(self):
        self.dict_bool['트레이더시작'] = True
        self.xar_O0.AddRealData() 
        self.xar_C0.AddRealData()
        self.teleQ.put('트레이더 시작')

    def PutJangoDF(self):        
        if not self.dict_bool['프로세스종료']:
            try:
                df = self.df_jg.copy()
                self.CallStrQ.put(df)
                self.PutStrQ.put(df)
                self.receiverQ.put(['잔고', df])
            except:
                pass
        else:
            pass
            
    def UpdateTotaljango(self):     
        tsg = 0       
        if len(self.df_jg) > 0:
            tsg = self.df_jg['평가손익'].sum()
            tbg = self.df_jg['매입금액'].sum()
            tpg = self.df_jg['평가금액'].sum()
            tsp = 0.0
            if tbg != 0:
                tsp = round(tsg / tbg *100, 2)
            else:
                pass
            self.df_tg.loc[self.dict_strg['당일날짜']] = self.dict_intg['당일실현손익'], tsg, tsp, tbg, tpg

        else:
            self.df_tg.loc[self.dict_strg['당일날짜']] = self.dict_intg['당일실현손익'], 0, 0.0, 0, 0    

        ############################################
        self.windowQ.put([ui_num['잔고목록'], self.df_jg.copy()])
        self.windowQ.put([ui_num['실현손익'], self.df_tg.copy()])
           
    def SysExit(self):
        self.dict_bool['프로세스종료'] = True
        self.qtimer1.stop()
        self.qtimer2.stop()
        self.updater.quit()
        self.teleQ.put('트레이더 종료')
        self.windowQ.put([ui_num['로그텍스트'], '시스템 명령 실행 - 트레이더 종료'])        
        
    def OnReceiveJeobsuData(self, data):
        try:
            code = data['fnoIsuno']
            oq = int(data['ordqty'])
            omc  = int(data['unercqty'])
            oct = 0
        except Exception as e:
            self.windowQ.put([ui_num['로그텍스트'], f'시스템 명령 오류 - OnReceiveJeobsuData {e}'])
        else:
            self.jeobsu_dic[code] = [code, oq, omc, oct]            
    
    def OnReceiveChegeolData(self, data):
        try:
            code = data['expcode'][3:-1]            
            og   = '매도' if data['dosugb'] == '1' else '매수'
            op   = float(data['cheprice'])
            oc   = int(data['chevol'])
            ct   = data['chetime']
        except Exception as e:
            self.windowQ.put([ui_num['로그텍스트'], f'시스템 명령 오류 - OnReceiveChegeolData {e}'])
        else:
            self.UpdateChejanData(code, '체결', og, op, oc, ct)

    def UpdateChejanData(self, code, ot, og, op, oc, ct):
        index = strf_time("%Y%m%d%H%M%S%f")
        if index in self.df_cj.index:
            while index in self.df_cj.index:
                index = str(int(index) + 1)
        op1 = op * 250_000
        if ot == '체결':
            if og == '매수':
                if code in self.df_jg.index:
                    jc = self.df_jg["보유수량"][code] + oc
                    jg = self.df_jg["매입금액"][code] + oc * op1
                    jp = round(jg / (jc * 250_000), 3)
                    pg, sg, sp = GetOptionLongPgSgSp(jg, jc * op1) 
                    columns = ['보유수량', '매입가', '현재가', '수익률', '평가손익', '매입금액', '평가금액', '구분'] 
                    self.df_jg.loc[code, columns] = jc, jp, op, sp, sg, jg, pg, og 
                else:
                    jc = oc
                    jg = oc * op1
                    jp = op
                    pg, sg, sp = GetOptionLongPgSgSp(jg, jc * op1) 
                    columns = ['종목코드', '보유수량', '매입가', '현재가', '수익률', '평가손익', '매입금액', '평가금액', '구분'] 
                    self.df_jg.loc[code, columns] = code, jc, jp, op, sp, sg, jg, pg, og
                    self.receiverQ.put(['잔고편입', code])

                self.PutJangoDF()

            elif og == '매도':
                if code not in self.df_jg.index:
                        return
                
                jc = self.df_jg["보유수량"][code] - oc 
                jp = self.df_jg["매입가"][code]

                if jc != 0:
                    jg = jp * jc * 250_000
                    pg, sg, sp = GetOptionLongPgSgSp(jg, jc * op1)
                    columns = ["보유수량", "현재가", "수익률", "평가손익", "매입금액", "평가금액"]
                    self.df_jg.loc[code, columns] = jc, op, sp, sg, jg, pg 
                else:
                    self.df_jg.drop(index=code, inplace=True)
                    self.receiverQ.put(['잔고청산', code])

                jg = jp * oc * 250_000
                pg, sg, sp = GetOptionLongPgSgSp(jg, jc * op1)
            
            columns = ["평가손익", "매입금액", "평가금액", "보유수량"]
            self.df_jg[columns] = self.df_jg[columns].astype(int)
            columns = ["매입가", "현재가"]
            self.df_jg[columns] = self.df_jg[columns].astype(float)
            self.df_jg.sort_values(by=["매입금액"], ascending=False, inplace=True)

            self.PutJangoDF()

            if og == '매수':
                self.windowQ.put([ui_num['로그텍스트'], f'[{ot}] {ct} | {code} | {op} | {oc} | {og}'])
                self.teleQ.put(f'[{ot}] {ct} | {code} | {op} | {oc} | {og}')
                
            elif og == '매도':
                og = '매수청산'
                self.windowQ.put([ui_num['로그텍스트'], f'[{ot}] {ct} | {code} | {op} | {oc} | {og}'])
                self.teleQ.put(f'[{ot}] {ct} | {code} | {op} | {oc} | {og}')

    def UpdateTradelist(self, index, code, jg, pg, cc, sp, sg, ct):
        self.df_td.loc[index] = code, jg, pg, cc, sp, sg, ct
        self.windowQ.put([ui_num['거래목록'], self.df_td[::-1]])
        self.windowQ.put([ui_num['단순텍스트'], f'거래목록: {code, jg, pg, cc, sp, sg, ct}'])

        df = pd.DataFrame([[code, jg, pg, cc, sp, sg, ct]], columns=columns_td, index=[index])
        self.UpdateTotaltradelist()

    def UpdateTotaltradelist(self, first=False):
        tdt = len(self.df_td.drop_duplicates(["종목코드", "체결시간"]))
        tbg = self.df_td["매수금액"].sum()
        tsg = self.df_td["매도금액"].sum()
        sig = self.df_td[self.df_td["수익금"] > 0]["수익금"].sum()
        ssg = self.df_td[self.df_td["수익금"] < 0]["수익금"].sum()
        sg = self.df_td["수익금"].sum()
        if tbg != 0:
            sp  = round(sg / tbg  * 100, 2)
        self.dict_intg["당일실현손익"] = sg

        self.df_tt = pd.DataFrame([[tbg, tsg, sig, ssg, sp, sg]], columns=columns_tt, index=[self.dict_strg["당일날짜"]])
        self.windowQ.put([ui_num['실현손익'], self.df_tt])
        self.teleQ.put(f"거래횟수 {tdt}회 / 수익금합계 {int(sg):,}원")

    def get_0441(self) :   
        tr_code = 't0441'
        INBLOCK = "%sInBlock" % tr_code
        INBLOCK1 = "%sInBlock1" % tr_code
        OUTBLOCK = "%sOutBlock" % tr_code
        OUTBLOCK1 = "%sOutBlock1" % tr_code
        OUTBLOCK2 = "%sOutBlock2" % tr_code
        OUTBLOCK3 = "%sOutBlock3" % tr_code
        
        # INI = ConfigparserINI()

        query = win32com.client.DispatchWithEvents("XA_DataSet.XAQuery", XAQueryEventHandler)
        query.ResFileName = "C:\\LS_SEC\\xingAPI\\Res\\"+tr_code+".res"
        query.SetFieldData(INBLOCK, "accno", 0, self.INI.AccNr)
        query.SetFieldData(INBLOCK, "passwd", 0, self.INI.TradePW) 
        query.SetFieldData(INBLOCK, "cts_expcode", 0, '')
        query.SetFieldData(INBLOCK, "cts_medocd", 0, '')
        query.Request(0)

        ret = wait_for_event(tr_code)
        if ret == 0 :
            return [{'error':{'message':tr_code+' msg error'}}]

        result = []
        result2 = []
        try:
            tr_profit = str(query.GetFieldData(OUTBLOCK, "tdtsunik", 0)) #매매손익합
            total = str(query.GetFieldData(OUTBLOCK, "tappamt", 0)) #평가금액  # 매수/매도 구분이 없이 합임. 실제로는 매수/매도 구분하여 +-필요
            profit = str(query.GetFieldData(OUTBLOCK, "tsunik", 0)) #평가손익
        except TypeError:
            pass
        try:
            lst = {'tr_profit':tr_profit, 'total':total, 'profit':profit}
            result.append(lst)
        except ValueError: 
            pass  

        nCount = query.GetBlockCount(OUTBLOCK1)
        for i in range(nCount):       
            code = query.GetFieldData(OUTBLOCK1, "expcode", i).strip() #종목코드
            buy_sell = query.GetFieldData(OUTBLOCK1, "medosu", i).strip() #
            qty = query.GetFieldData(OUTBLOCK1, "jqty", i) #잔고수량
            orderable_qty = query.GetFieldData(OUTBLOCK1, "cqty", i) #청산가능수량
            buy_sell_add = query.GetFieldData(OUTBLOCK1, "medocd", i).strip() #매매구분
            price = query.GetFieldData(OUTBLOCK1, "price", i) #현재가
            unitorderprice = query.GetFieldData(OUTBLOCK1, "pamt", i) #평균단가
            appamt = query.GetFieldData(OUTBLOCK1, "appamt", i) #평가금액
            mamt = query.GetFieldData(OUTBLOCK1, "mamt", i) # 총매입금액
            profitrate = query.GetFieldData(OUTBLOCK1, "sunikrt", i) # 수익율
    
            lst = {'code':code, 'buy_sell':buy_sell, 'qty':int(qty), 'orderable_qty':int(orderable_qty), 'buy_sell_add':buy_sell_add, 'price':float(price), \
            'unitorderprice':float(unitorderprice), 'appamt':int(appamt), 'mamt':int(mamt), 'profitrate': float(profitrate)}
            result2.append(lst)

        result.append(result2)
        return result

def wait_for_event(code) :
    while XAQueryEventHandler.query_state == 0:
        pythoncom.PumpWaitingMessages()

    if XAQueryEventHandler.query_code != code :
        print('diff code : wish(', code,')', XAQueryEventHandler.query_code)
        return 0
    XAQueryEventHandler.query_state = 0
    XAQueryEventHandler.query_code = ''
    return 1

class XAQueryEventHandler:
    query_state = 0
    query_code = ''
    T1102_query_state = 0
    T8413_query_state = 0

    def OnReceiveData(self, code):
        XAQueryEventHandler.query_code = code
        XAQueryEventHandler.query_state = 1
