import struct
from utils.util import * 
from utils.configparserINI import ConfigparserINI

INI = ConfigparserINI()
ICON_PATH     = './icon'
BIT32  = True if struct.calcsize('P'*8) == 32 else False  

list_hs = ""

filePath1 = './창위치.txt'        
with open(filePath1, 'r', encoding="UTF-8") as f:
    list_hs = f.read()  

ui_num = {'설정로그': 1, '단순텍스트': 2, '로그텍스트': 3, '거래옵션': 4,
          '실현손익': 11, '거래목록': 12, '잔고평가': 13, '잔고목록': 14, '체결목록': 15,
          '당일합계': 16, '당일상세': 17, '누적합계': 18, '누적상세': 19, '관심종목': 20 }

columns_tg = ['당일실현손익', '평가손익', '수익율', '매입금액', '옵션평가금액']
columns_jg = ['종목코드', '보유수량', '매입가', '현재가', '수익율', '평가손익', '매입금액', '평가금액', '구분']
columns_tt = ['총매수금액', '총매도금액', '총수익금액', '총손실금액', '수익률', '수익금합계'] 
columns_td = ['종목코드', '매수금액', '매도금액', '주문수량', '수익률', '수익금', '체결시간']
columns_cj   = ['종목코드', '주문구분', '주문수량', '체결수량', '미체결수량', '체결가', '체결시간', '주문가격', '주문번호']

DICT_SET = {
    '리시버':    False,
    '트레이더':  False,
    'Xing트레이더': False,

    '텔레그램사용자아이디': INI.teleID,
    '텔레그램봇토큰': INI.teleToken,
    '창위치': [int(x) for x in list_hs.split(';')[:-1]] if list_hs != '' else None,
}
holidays_M = {"2025": {"0303", "0505", "0506"}} 
holidays_T = {"2025": {"1225"}} 

OptFutY = {
    '2025': 'W', '2026':'6', '2027': '7', '2028':'8', '2029': '9', '2030': '0', '2031': '1', '2032': '2', '2033': '3', '2034':'4', '2035': '5', '2036': '6', '2037':'7', '2038': '8'
}  
OptFutM = {
    '1': '1', '2': '2', '3': '3', '4': '4', '5': '5', '6': '6', '7': '7', '8': '8', '9': '9', '10': 'A', '11': 'B', '12': 'C'
}  
OptFutD = {
    '01': '9', '02': '13', '03': '13', '04': '10', '05': '08', '06': '12', '07': '10', '08': '14', '09': '11', '10': '9', '11': '13', '12': '11'
} 