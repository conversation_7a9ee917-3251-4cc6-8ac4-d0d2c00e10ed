def getHMTFromTime(time, interval):
    hh, mm = divmod(time, 10000)
    mm, tt = divmod(mm, 100)
    
    if (mm + 15) >= 60:
        mmt = (mm + 15) % 60
        converedSectime = (hh-8) * 60*60 + (mmt)*60 + tt
    else:
        converedSectime = (hh-9) * 60*60 + (mm + 15)*60 + tt
    a = 0
    if interval != 0 :
        a, b = divmod(converedSectime, interval)
    else:
        pass
    intervaltime = a * interval
   
    return intervaltime

def getChartTime(time, interval):
    
    # time 600 (10:00 인 경우 600)
    lChartTimebefore = time
    lChartTime = time + interval
    
    hourc, minc = divmod(lChartTimebefore, 60*60)
    hour, min = divmod(lChartTime, 60*60)
    
    min_c, sec_c = divmod(minc, 60)
    min, sec = divmod(min, 60)

    if (min + 45) >= 60:
        mint = (min + 45) % 60
        lCurTime = (hour + 9) * 100*100 + (mint)*100 + sec
    else:
        lCurTime = (hour + 8) * 100*100 + (min + 45)*100 + sec

    if (min_c + 45) >= 60:
        mintc = (min_c + 45) % 60
        lChartTimebefore = (hourc + 9) * 100*100 + (mintc)*100 + sec
    else:
        lChartTimebefore = (hourc + 8) * 100*100 + (min_c + 45)*100 + sec_c
    

    return str(lChartTimebefore) + ".000000", str(lCurTime) + ".000000" 

