import telegram
import pandas as pd
from utils.config import DICT_SET
from telegram.ext import <PERSON><PERSON><PERSON>, MessageHandler, Filters


class TelegramMsg:
    def __init__(self, qlist):       
        self.receiverQ      = qlist[1]
        self.traderQ        = qlist[2]
        self.CallStrQ       = qlist[3]
        self.PutStrQ        = qlist[4]
        self.teleQ          = qlist[5]
        
        self.dict_set = None
        self.updater  = None
        self.bot      = None
        self.UpdateBot(DICT_SET)
        self.Start()

    def Start(self):
        while True:
            data = self.teleQ.get()
            if type(data) == str:
                self.SendMsg(data)
            elif type(data) == pd.DataFrame:
                self.UpdateDataframe(data)
            elif type(data) == dict:
                if self.updater is not None:
                    self.updater.stop()
                    self.updater = None
                self.UpdateBot(data)

    def __del__(self):
        if self.updater is not None:
            self.updater.stop()

    def UpdateBot(self, dict_set):
        self.dict_set = dict_set
        if self.updater is None and self.dict_set['텔레그램봇토큰'] is not None:
            try:
                self.bot = telegram.Bot(self.dict_set['텔레그램봇토큰'])
            except:
                print('텔레그램 설정 오류 알림 - 텔레그램 봇토큰이 잘못되어 봇을 만들 수 없습니다.')
            else:
                self.SetCustomButton()
        else:
            self.bot = None

    def SetCustomButton(self):
        custum_button = [
            ['체결목록', '거래목록', '잔고평가'] 

        ]
        reply_markup = telegram.ReplyKeyboardMarkup(custum_button)
        self.bot.send_message(chat_id=self.dict_set['텔레그램사용자아이디'], text='사용자버튼 설정을 완료하였습니다.', reply_markup=reply_markup)
        self.updater = Updater(self.dict_set['텔레그램봇토큰'])
        self.updater.dispatcher.add_handler(MessageHandler(Filters.text, self.ButtonClicked))
        self.updater.start_polling(drop_pending_updates=True)

    def ButtonClicked(self, update, context):
        if context == '':
            return
        cmd = update.message.text
        if cmd == '체결목록' or cmd == '거래목록' or cmd == '잔고평가':
            self.traderQ.put(cmd)

    def SendMsg(self, msg):
        if self.bot is not None:
            try:
                self.bot.sendMessage(chat_id=self.dict_set['텔레그램사용자아이디'], text=msg)
            except Exception as e:
                print(f'텔레그램 명령 오류 알림 - SendMsg {e}')
        else:
            print('텔레그램 설정 오류 알림 - 텔레그램 봇이 설정되지 않아 메세지를 보낼 수 없습니다.')

    def UpdateDataframe(self, df):
        if df.columns[1] == '매수금액':
            text = ''
            for index in df.index:
                ct    = df['체결시간'][index][8:10] + ':' + df['체결시간'][index][10:12]
                per   = df['수익률'][index]
                sg    = df['수익금'][index]
                name  = df['종목명'][index]
                text += f'{ct} {per:.2f}% {sg:,.0f}원 {name}\n'
            self.SendMsg(text)
        elif df.columns[1] in ['매입가', '포지션']:
            text   = ''
            m_unit = '원' if df.columns[1] == '매입가' else 'USDT'
            for index in df.index:
                per   = df['수익률'][index]
                sg    = df['평가손익'][index]
                name  = df['종목명'][index]
                if df.columns[1] == '매입가':
                    text += f'{per:.2f}% {sg:,.0f}{m_unit} {name}\n'
                else:
                    pos   = df['포지션'][index]
                    text += f'{pos} {per:.2f}% {sg:,.0f}{m_unit} {name}\n'
            tbg   = df['매입금액'].sum()
            tpg   = df['평가금액'].sum()
            tsg   = df['평가손익'].sum()
            tsp   = round(tsg / tbg * 100, 2) if tbg != 0 else 0
            text += f'{tbg:,.0f}{m_unit} {tpg:,.0f}{m_unit} {tsp:.2f}% {tsg:,.0f}{m_unit}\n'
            self.SendMsg(text)
        elif df.columns[1] == '주문구분':
            text = ''
            for index in df.index:
                ct   = df['체결시간'][index][8:10] + ':' + df['체결시간'][index][10:12]
                bs   = df['주문구분'][index]
                bp   = df['체결가'][index]
                name = df['종목명'][index]
                text += f'{ct} {bs} {bp:,.0f} {name}\n'
            self.SendMsg(text)
