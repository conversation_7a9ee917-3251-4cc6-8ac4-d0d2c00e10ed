pyinstaller -w --uac-admin --onefile --hidden-import talib.stream --noconsole -F -n OptionTraderM.exe -c --clean OptionTraderM.py


a = Analysis(
    ['OptionTraderM.py'],
    pathex=[],
    binaries=[],
    datas=[('./icon/*', './icon'),
            ('./utils/*', './'),
            ('config_setting.ini', './'),
   ('창위치.txt', './'),
    ],      
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)


